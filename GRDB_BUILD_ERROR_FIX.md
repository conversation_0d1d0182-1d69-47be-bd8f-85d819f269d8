# GRDB Build Error Fix Guide

## Problem
Build error: `Unable to resolve build file: BuildFile<PACKAGE-TARGET:GRDB-5DC4DB053-dynamic::BUILDPHASE_1::0> (The workspace has a reference to a missing target with GUID 'PACKAGE-TARGET:GRDBSQLite')`

## Root Cause
- GRDB is pinned to `master` branch (unstable)
- Version mismatch between GRDB and GRDBSQLite targets
- Swift Package Manager cache corruption

## Solutions (Choose One)

### Solution 1: Update GRDB to Stable Version (Recommended)
1. **Open Xcode**
2. **Go to File → Add Package Dependencies**
3. **Find GRDB in the list and remove it**
4. **Add GRDB again with stable version:**
   - URL: `https://github.com/groue/GRDB.swift.git`
   - Version: `Up to Next Major Version` starting from `6.0.0`

### Solution 2: Manual Package Reset
```bash
# Clean all caches
rm -rf .build
rm -rf ~/Library/Developer/Xcode/DerivedData/Modiesha-*

# Reset package dependencies
xcodebuild -resolvePackageDependencies

# Clean and rebuild
xcodebuild clean
xcodebuild build
```

### Solution 3: Remove GRDB Temporarily (Quick Fix)
If GRDB is not critical for VPN functionality:

1. **Open Xcode**
2. **Select Modiesha project**
3. **Go to Package Dependencies tab**
4. **Remove GRDB.swift package**
5. **Comment out GRDB imports in affected files:**
   - `Library/Database/Profile+Update.swift`
   - `Library/Database/ShadredPreferences+Database.swift`
   - `Library/Database/Profile.swift`
   - `Library/Database/ProfileManager.swift`
   - `Library/Database/Databse.swift`

### Solution 4: Fix GRDB Version in project.pbxproj
Change the GRDB requirement from `branch = master` to a stable version:

```xml
requirement = {
    kind = upToNextMajorVersion;
    minimumVersion = 6.0.0;
};
```

## Current GRDB Usage
GRDB is used for:
- Profile management
- Database operations
- Shared preferences storage

## Impact on VPN Functionality
- ✅ **VPN Connection**: Not affected
- ✅ **Configuration Management**: Not affected  
- ✅ **Sing-Box Integration**: Not affected
- ❌ **Profile Database**: Will be disabled if GRDB is removed

## Recommended Action
1. **Try Solution 1 first** (update to stable version)
2. **If that fails, use Solution 3** (remove temporarily)
3. **Focus on VPN functionality** which is working
4. **Re-add GRDB later** when needed for profile features

## Testing After Fix
```bash
# Test build
xcodebuild -project Modiesha.xcodeproj -scheme Modiesha -destination 'platform=iOS,id=YOUR_DEVICE_ID' build

# If successful, test VPN functionality
# - Import configuration file
# - Select configuration
# - Connect VPN
# - Verify connection status
```

## Files That May Need GRDB Import Removal
If removing GRDB temporarily:
- `Library/Database/Profile+Update.swift`
- `Library/Database/ShadredPreferences+Database.swift`
- `Library/Database/Profile.swift`
- `Library/Database/ProfileManager.swift`
- `Library/Database/Databse.swift`

Comment out lines like:
```swift
// import GRDB
```

And replace GRDB functionality with simple in-memory storage or UserDefaults for now.
