// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		01C725BA646437F24BF280B73E8B3DC0 /* Database+Dump.swift in Sources */ = {isa = PBXBuildFile; fileRef = 96DEFF6D3ECE2C89F1120C47CCC6BF0C /* Database+Dump.swift */; };
		046AECD21FBF3338A490615B157AADD1 /* StatementColumnConvertible.swift in Sources */ = {isa = PBXBuildFile; fileRef = B86DB54C8B71DBBF943081C7BC174D26 /* StatementColumnConvertible.swift */; };
		04C16EE845D2CEAF7A0E433E67B93028 /* DatabaseCollation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 897F3FC97B2081FC917411BF4590D32C /* DatabaseCollation.swift */; };
		05EE813C6C3CB374EE41B8DD3C84D5B3 /* Inflections+English.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1D0E0177B1367FD207DD0768F52E0BA8 /* Inflections+English.swift */; };
		068CFEB7D45924AC6A10ED742F0FDFA2 /* Database+SchemaDefinition.swift in Sources */ = {isa = PBXBuildFile; fileRef = 07B47A9C3E533110DD7F5AE25761EB1A /* Database+SchemaDefinition.swift */; };
		07C70C592165C5759D92C570595A6308 /* SQLJSONExpressible.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A262FB4924F5CD64B55DC036317EE28 /* SQLJSONExpressible.swift */; };
		0A31F5EA78AFA1273696C973431369EC /* Trace.swift in Sources */ = {isa = PBXBuildFile; fileRef = 66F728148ADC5662C8ED900A2BA5BFB5 /* Trace.swift */; };
		0A5E233E72F68CF7489EB9DBE7CDF4F6 /* JoinAssociation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 61263E84786D15358777F9F2CC0E7D03 /* JoinAssociation.swift */; };
		0B395D2CA34FF29003259CD0B621695C /* HasManyAssociation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9F1EBC3B29513F987DF553C66CCD7F26 /* HasManyAssociation.swift */; };
		0BA95943BCAF0506500A17E7AAADF9F5 /* PersistableRecord+Save.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4178530D5612259DA2FA3F3858DD4F12 /* PersistableRecord+Save.swift */; };
		0BE3BEEE6DD25252470315E77193947B /* UUID.swift in Sources */ = {isa = PBXBuildFile; fileRef = AC809412C67409D65F131ACA3C1C9005 /* UUID.swift */; };
		0CC206146A5DBB56C7E9C3AD7EDA3781 /* ColumnDefinition.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9B7A1B09EF673371DB70CE9BD8F598D8 /* ColumnDefinition.swift */; };
		******************************** /* Map.swift in Sources */ = {isa = PBXBuildFile; fileRef = ******************************** /* Map.swift */; };
		0FB973490139F3E6A9DF913D3D6F4EAF /* Inflections.swift in Sources */ = {isa = PBXBuildFile; fileRef = F6A858103A81F9A3BD13BBF359AC501F /* Inflections.swift */; };
		108502126D9CED9969A936103A832B35 /* OnDemandFuture.swift in Sources */ = {isa = PBXBuildFile; fileRef = 87FE65AA0EDEB1E83CB67C338E1B33D6 /* OnDemandFuture.swift */; };
		122770537F9A40A084D46CB3C3DEA46B /* DatabaseSchemaCache.swift in Sources */ = {isa = PBXBuildFile; fileRef = 321E71AC72C7C068240A76EF7231C33C /* DatabaseSchemaCache.swift */; };
		138B831FB587C94E8288BAA6871BAA56 /* SQL.swift in Sources */ = {isa = PBXBuildFile; fileRef = 10FC594F337E9FEB48BBBC8618B8D7DE /* SQL.swift */; };
		1B0A360705A76416DCF4CF58EE8B4ABC /* HasOneAssociation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 87EE6FB45D98A10D5F2C1B32DE597EA7 /* HasOneAssociation.swift */; };
		1C0B9ED8F8D46398AB81A246B7C2CC91 /* SQLSubquery.swift in Sources */ = {isa = PBXBuildFile; fileRef = 44216A4D9279269162BD9014BE0AAED4 /* SQLSubquery.swift */; };
		1D5638DA09DFE646F23AE3B1A6B25D15 /* FTS5Pattern.swift in Sources */ = {isa = PBXBuildFile; fileRef = 22334CC0DC7B4F46313D8CDB3F7F28A0 /* FTS5Pattern.swift */; };
		1E76A5A8A6A182CA47A33F8580E2E1D5 /* HasOneThroughAssociation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 950B9CA657F2617EDED101A99F3EE5C7 /* HasOneThroughAssociation.swift */; };
		1EFCBAC2136290B35A7364342D3DED6E /* SQLOperators.swift in Sources */ = {isa = PBXBuildFile; fileRef = ABFF93A854CFE71C275D09ED847B98DC /* SQLOperators.swift */; };
		1F5D4AE21E45A22579F6F1604CF4C3A2 /* RowAdapter.swift in Sources */ = {isa = PBXBuildFile; fileRef = D238CAE50DD6BA3D07C855A0243CDE74 /* RowAdapter.swift */; };
		207361AB8E442E2B334189A40D3F83F2 /* PersistableRecord+Upsert.swift in Sources */ = {isa = PBXBuildFile; fileRef = AAEFDB9140E8D7FC5EA25E00A0EBB8B4 /* PersistableRecord+Upsert.swift */; };
		2359F870120FA49BFAAF48CE41B70DE7 /* SQLColumnGenerator.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8542EB95445579DC0C67C29B19FFCC7A /* SQLColumnGenerator.swift */; };
		25EA7D0A2FD4DA2087F9F41924CD8E87 /* GRDB.swift-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = FB5584BFB9E4DBFBC980931D1A713791 /* GRDB.swift-dummy.m */; };
		274C2FCA2808D52117D7C2DE355D4B0A /* SQLTableGenerator.swift in Sources */ = {isa = PBXBuildFile; fileRef = C07777F3CD33534A44167ED3F2C4EBB1 /* SQLTableGenerator.swift */; };
		280871BA26C76A6B5CA7DF3DD016E2FE /* NSData.swift in Sources */ = {isa = PBXBuildFile; fileRef = C448D5D4C453E874A234EBBD266877E8 /* NSData.swift */; };
		29679F65DC29FA03F351AA1B7B45ACA9 /* Pods-ModieshaAbstract-ModieshaNetworkExtension-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 93A6705E6BABC9E73591FBA1D15EA9D0 /* Pods-ModieshaAbstract-ModieshaNetworkExtension-dummy.m */; };
		2A4FFB04A0EA46438DC6CB23F047743F /* DatabaseCancellable.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2C99D87D1E10E59A4013A4EE24A752D9 /* DatabaseCancellable.swift */; };
		2C0E271BBFB9E1A29D9895248FF44A03 /* FTS5CustomTokenizer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0DF5A81B026D5BE13D40FFF53710314B /* FTS5CustomTokenizer.swift */; };
		3398C643E437D24503E9EC98AB56EB38 /* SQLCollection.swift in Sources */ = {isa = PBXBuildFile; fileRef = CCCE12721BC44C92694FC759D4967FAB /* SQLCollection.swift */; };
		37BD4A55FC28E6B1A610FD3A2376B15C /* Database.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1583CC47BEDD1F7693E8531540F8B9AA /* Database.swift */; };
		39DF54D359A58F3FDAFA973EC432C401 /* DatabasePool.swift in Sources */ = {isa = PBXBuildFile; fileRef = C9EF6B1375FE4D341B96D6D98BD64D0C /* DatabasePool.swift */; };
		3A36772FF42CA5B29FBA03D24703A7BF /* Fetch.swift in Sources */ = {isa = PBXBuildFile; fileRef = F6A5EA9757AD0F4278108347CBCB68D9 /* Fetch.swift */; };
		3AEED39A57CDFAB925B46667E46BA7EC /* PersistableRecord.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8A91D0A9DDA70D06730E8AE4B2F0DB74 /* PersistableRecord.swift */; };
		3DCDCAB63AD77FCC8678F7A5B45CE8C5 /* TableRecord.swift in Sources */ = {isa = PBXBuildFile; fileRef = 47EBFCC60F1F01C8D8599DEB8653799C /* TableRecord.swift */; };
		3FF3B3A8EAF5346BB9FC6EA7B97B9ACF /* SQLiteDateParser.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2304C1763E945E54C3C28E9485174327 /* SQLiteDateParser.swift */; };
		40B473B444920D42A390924414A0615A /* NSNull.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5311AD1A0301473E6E953DBB50802600 /* NSNull.swift */; };
		40F7443B414418C95439F04438687FA0 /* QuoteDumpFormat.swift in Sources */ = {isa = PBXBuildFile; fileRef = BF0FF276B04BEB26AFC029B8CC5737E2 /* QuoteDumpFormat.swift */; };
		44A4365B0A0EED72AB71CC0E59C36883 /* BelongsToAssociation.swift in Sources */ = {isa = PBXBuildFile; fileRef = AC6F69A431347A923AE32E711AF1036B /* BelongsToAssociation.swift */; };
		46DCB5B1AB91AF27ABF6B9633BF9CF20 /* DatabaseValueConvertible.swift in Sources */ = {isa = PBXBuildFile; fileRef = 40B6FC2033DD5642D2B395CF9AC509ED /* DatabaseValueConvertible.swift */; };
		46DCFFFF7C352F9E8DE1F03E2DC387A6 /* VirtualTableModule.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2F5E2B9AA26CC514E436FEEDF709C9E7 /* VirtualTableModule.swift */; };
		47802F0AE1E52F0139B8875C935C5E0A /* PersistableRecord+Insert.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6F4883C0CAAB145A8CF3D790831C78E5 /* PersistableRecord+Insert.swift */; };
		47F75CC7252EBFA9747AAD6F729A7E4F /* SQLTableAlterationGenerator.swift in Sources */ = {isa = PBXBuildFile; fileRef = AB918EBFAC48537D25DE47CC7836F66B /* SQLTableAlterationGenerator.swift */; };
		4889CF223D36C71EB04F36DE6062508F /* Configuration.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7D64A2B2338483128C97F43E6458CF6E /* Configuration.swift */; };
		4B4A72804C3F6C00B31F3106653B0DF7 /* FTS5Tokenizer.swift in Sources */ = {isa = PBXBuildFile; fileRef = E63DB5BD9A31A74804661E5BF28B3665 /* FTS5Tokenizer.swift */; };
		4C6391CDB5EACF86798402C835AF56DD /* CommonTableExpression.swift in Sources */ = {isa = PBXBuildFile; fileRef = 37BF8404677F899C6C30BAA59253D42F /* CommonTableExpression.swift */; };
		4CE7F6A649E847355C154CD34A14B33C /* DatabaseRegionObservation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2D0614CE22C1D5EB335D8998F907F883 /* DatabaseRegionObservation.swift */; };
		4D5F42A43B21113BC99D47AADACA064D /* TransactionObserver.swift in Sources */ = {isa = PBXBuildFile; fileRef = BFD070367F2F319AD6CF933D97801B70 /* TransactionObserver.swift */; };
		4FBCAA689F9C5B3D0349456C242B45E7 /* ListDumpFormat.swift in Sources */ = {isa = PBXBuildFile; fileRef = A50E9AEBBC53BE59D698CB96E9356768 /* ListDumpFormat.swift */; };
		511BA44015032AFAF62CF7D295D5A35C /* URL.swift in Sources */ = {isa = PBXBuildFile; fileRef = D1B4153E8A05D0614CD3D7C5D4E85622 /* URL.swift */; };
		511CB376AF3CF90247A470A47C359579 /* QueryInterfaceRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 31A3311B39CA89447090A260F98A374B /* QueryInterfaceRequest.swift */; };
		521B6E6177E3B70DBEB68D67D78B58AF /* SharedValueObservation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7FBAB9476E2C3CB744FC7DA2E200F4EB /* SharedValueObservation.swift */; };
		568336004A1E7D9D8E3F04BCC0368483 /* RemoveDuplicates.swift in Sources */ = {isa = PBXBuildFile; fileRef = 05C0CF7729B3BD44508725A598A967DC /* RemoveDuplicates.swift */; };
		56E6660506B4A337650D2DFCB70BF834 /* ForeignKeyDefinition.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6D4980C3E9F28FCB22C50249D9075068 /* ForeignKeyDefinition.swift */; };
		579BBD9CEE26494007F147B14A6A24F1 /* DatabaseError.swift in Sources */ = {isa = PBXBuildFile; fileRef = 51C4EB813981C04D0308AEE260A4327C /* DatabaseError.swift */; };
		5D6C0D8DB6B4B36CB6672435837DDF9A /* SQLOrdering.swift in Sources */ = {isa = PBXBuildFile; fileRef = C5C0B086A2F93840BDCE1F8C7A5F1EE6 /* SQLOrdering.swift */; };
		6080FC7FD24312978665FEBF2A635040 /* Pods-ModieshaAbstract-Modiesha-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 555916DFDA1773EBAD92C5E930EC715C /* Pods-ModieshaAbstract-Modiesha-umbrella.h */; settings = {ATTRIBUTES = (Project, ); }; };
		60C37199111DEFA343B4F030FDEB6A38 /* MutablePersistableRecord+Save.swift in Sources */ = {isa = PBXBuildFile; fileRef = AE39C1D9B5E717C79FF3602886448E3F /* MutablePersistableRecord+Save.swift */; };
		60F4AFFD4FF5EBD37D18A586CF6691E3 /* FTS4.swift in Sources */ = {isa = PBXBuildFile; fileRef = C7A9A6ED9B5A33578A78F217C2409143 /* FTS4.swift */; };
		6206EACBC2A1C270C714C982624EC60E /* DatabaseSnapshotPool.swift in Sources */ = {isa = PBXBuildFile; fileRef = 62D99F0E489DAFFB3BBC32D8714D2627 /* DatabaseSnapshotPool.swift */; };
		6861A1E2F13EB483894E95A46FBDDE42 /* FTS5TokenizerDescriptor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 76B279BAEC64F976C831791D9F6084EF /* FTS5TokenizerDescriptor.swift */; };
		68D9B3C0458FF0101EAE6ADEA4D46662 /* Record.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7BF85F6F2ACEABEEFACBC4CA17B56085 /* Record.swift */; };
		6C590CAD0BBBD4E2376CFFC47D243206 /* TableRecord+Association.swift in Sources */ = {isa = PBXBuildFile; fileRef = C22EEE9B2C1992A8358C3EF7469E88AE /* TableRecord+Association.swift */; };
		6CD16FAC56454DC593E60598C3CEC5C2 /* ValueWriteOnlyObserver.swift in Sources */ = {isa = PBXBuildFile; fileRef = 68A89820B42F8589FAF2340F29D31BED /* ValueWriteOnlyObserver.swift */; };
		6DCF31F8D11CB6EC6C63DF4BE8D787B7 /* FTS5+QueryInterface.swift in Sources */ = {isa = PBXBuildFile; fileRef = DB5D25AC4EB905EF523B754AB99FA3C5 /* FTS5+QueryInterface.swift */; };
		6E5872F3A199DA240E8627F270251CA5 /* SQLJSONFunctions.swift in Sources */ = {isa = PBXBuildFile; fileRef = C4B8588DDC5999FC198F1CF4A2EC9B12 /* SQLJSONFunctions.swift */; };
		719D1A981BC527B38FB9E6B435A449FC /* GRDB.swift-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = B6CB1A3E4360B70F92D669B3856B0CC7 /* GRDB.swift-umbrella.h */; settings = {ATTRIBUTES = (Project, ); }; };
		7379860E031C6AED9376E5AB3EA3E1B6 /* LockedBox.swift in Sources */ = {isa = PBXBuildFile; fileRef = 38DEDC908B46EDAB870C1F79C5DE3E73 /* LockedBox.swift */; };
		******************************** /* Data.swift in Sources */ = {isa = PBXBuildFile; fileRef = 82B0B7B5D11A20DBE23140364BEC3DD4 /* Data.swift */; };
		745ED5BBBA769D79C2175E1EC3F20D24 /* Table.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6B9AD445394F39E5D2A69A543FF8BC8D /* Table.swift */; };
		74EE559C20ABCCE9B78BB8139587D5DA /* EncodableRecord.swift in Sources */ = {isa = PBXBuildFile; fileRef = 021AA890A0DCF0117F99A82A7887B50D /* EncodableRecord.swift */; };
		7641648EB92CBF0569E29DD752CDFCB5 /* SQLFunctions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1009E22874D53C33546C454BBB3CD884 /* SQLFunctions.swift */; };
		76CD7FBF2D100DD2B8D4DE7AED64BDD8 /* EncodableRecord+Encodable.swift in Sources */ = {isa = PBXBuildFile; fileRef = 23DF3F34024629A7EFDCA05A285A53BF /* EncodableRecord+Encodable.swift */; };
		7914EEF51E70148EA48B8BB12964325D /* ValueObservationScheduler.swift in Sources */ = {isa = PBXBuildFile; fileRef = F79E0BBB3949E7CE0D1BE00137D4CB59 /* ValueObservationScheduler.swift */; };
		7D267174D805DB21AD3557F9D39692AD /* Export.swift in Sources */ = {isa = PBXBuildFile; fileRef = C51ECB338F2B0DF1806C30357C0206B3 /* Export.swift */; };
		7E9DF8DCDE261ABE002A117CD28F600C /* AssociationAggregate.swift in Sources */ = {isa = PBXBuildFile; fileRef = B15331ECAA2DB0EE8B7F026273361A37 /* AssociationAggregate.swift */; };
		87F0746BFDCD45B8C46E02E2D74A37A7 /* MutablePersistableRecord+Upsert.swift in Sources */ = {isa = PBXBuildFile; fileRef = 14DF23AA4CE2CB94DCEE4BC4BFAD8FA8 /* MutablePersistableRecord+Upsert.swift */; };
		8A244918EF1DE6AEE6B9C76C1761F447 /* DatabasePromise.swift in Sources */ = {isa = PBXBuildFile; fileRef = C4BECD29EA2323E1F17BCD82D0A61333 /* DatabasePromise.swift */; };
		8AF221F84AE31B18AD7D65566618AAD2 /* WALSnapshot.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0C453FC093572099C191ED405BFF4138 /* WALSnapshot.swift */; };
		8E3967745DC97334097947C7874F990F /* FTS5WrapperTokenizer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 388B2F1CD028B8F3574599CCD6A9FDD2 /* FTS5WrapperTokenizer.swift */; };
		8F6C239985DACFEA248EA198892B05AE /* SchedulingWatchdog.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9FFD36A4AD5BD3EF8C77F20C09B4D253 /* SchedulingWatchdog.swift */; };
		8FF2E305CDE8EA4CFE832BFD6860428C /* SQLInterpolation.swift in Sources */ = {isa = PBXBuildFile; fileRef = BC35C2AF4BB8A01B2ADCD4812F3115E4 /* SQLInterpolation.swift */; };
		9088193B7F69CB2431E331F96C433CD9 /* MutablePersistableRecord+DAO.swift in Sources */ = {isa = PBXBuildFile; fileRef = C6CCFA10502F8EC4E4F2A30EC60A8269 /* MutablePersistableRecord+DAO.swift */; };
		91CC45A182AB6CBE4A331B98B07232C5 /* DatabaseValueConvertible+Decodable.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7F05BE80FB8AD9271DA034909154A81B /* DatabaseValueConvertible+Decodable.swift */; };
		922C8E6264A3030D185C3748E5AC5D3C /* JSONRequiredEncoder.swift in Sources */ = {isa = PBXBuildFile; fileRef = ACBD16187D363B104ABB2B7C22322048 /* JSONRequiredEncoder.swift */; };
		97BB035F44E8EE796D6BE06C42DA20F6 /* NSNumber.swift in Sources */ = {isa = PBXBuildFile; fileRef = ******************************** /* NSNumber.swift */; };
		97BB078BD5F9041D975425F678BE726E /* SQLGenerationContext.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4A7F10CADD50929EF5A5362CB7CFD322 /* SQLGenerationContext.swift */; };
		9B949BA71E23B8AFA2A3486D64039618 /* Row.swift in Sources */ = {isa = PBXBuildFile; fileRef = D511CCA94410FFC1737FB2B02887F4CB /* Row.swift */; };
		9B9B03495E56A652F94D614B1363391E /* ValueReducer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6183D4280DF1812C2E42BC14C99E66B9 /* ValueReducer.swift */; };
		9BD3CC38755E0B8AE5D9D8959FEB23B7 /* SQLRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1736B1EAEB34F5BF2DD1EFE8947280BE /* SQLRequest.swift */; };
		9BF46DB3441DA01E04011889B61D1EEA /* FetchableRecord.swift in Sources */ = {isa = PBXBuildFile; fileRef = 016E2061B097BFA66B10478D091044BA /* FetchableRecord.swift */; };
		9E9540690763E70F69FF8903573097CA /* MutablePersistableRecord+Insert.swift in Sources */ = {isa = PBXBuildFile; fileRef = 78C0D1A3B8E52B4A817276B7F7F78A7B /* MutablePersistableRecord+Insert.swift */; };
		9EFD54177423F253F8ABEF517D19144D /* CGFloat.swift in Sources */ = {isa = PBXBuildFile; fileRef = B31256FFA058F94343D3C656F2FEF4FC /* CGFloat.swift */; };
		9F502391B6D0A632CC8BED7ACD19E180 /* OrderedDictionary.swift in Sources */ = {isa = PBXBuildFile; fileRef = D0DCB20DCC76EE2208220451DEAFF6AA /* OrderedDictionary.swift */; };
		A0F4B98FC16B042EF7EFDC6B1339CAB5 /* Database+Schema.swift in Sources */ = {isa = PBXBuildFile; fileRef = 615EB3F81068E456BBD195FFEA5E4328 /* Database+Schema.swift */; };
		A1F29F0E14493C7DCE19B0156ABAEAD7 /* CaseInsensitiveIdentifier.swift in Sources */ = {isa = PBXBuildFile; fileRef = E74FE2194DCCF8DDA1E34655224B8156 /* CaseInsensitiveIdentifier.swift */; };
		A2170156C14C516584217F1843C0FE3D /* DatabaseQueue.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0620725A7D069E3ED5B5F55F5B944676 /* DatabaseQueue.swift */; };
		A2BFB3D0D509871593316D00E537D342 /* SQLAssociation.swift in Sources */ = {isa = PBXBuildFile; fileRef = A643D95E49C6F2F7A65F06B75CEF72EB /* SQLAssociation.swift */; };
		A37C69856BE94E58115989CDB1EB01F3 /* DebugDumpFormat.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3ABA6321FEF4A2863DEB4671D2D082A4 /* DebugDumpFormat.swift */; };
		A4525BB70FF4D60E96CDBC4788C4833C /* ValueObservation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9C6963559C18640F4D700BF51B4C1378 /* ValueObservation.swift */; };
		A50876A69CA5FD5C55F31D282F75224F /* DatabaseSnapshot.swift in Sources */ = {isa = PBXBuildFile; fileRef = 11852E03C78BAB0865A1D8726B5B4EFD /* DatabaseSnapshot.swift */; };
		A5B63E3A7CCC216680DEE3AAB3A33136 /* FTS5.swift in Sources */ = {isa = PBXBuildFile; fileRef = BFA6F0F5FE050B562C777D626942CB7C /* FTS5.swift */; };
		A7F1BE15B9B04F454F86489D49E74AA8 /* grdb_config.h in Headers */ = {isa = PBXBuildFile; fileRef = 096B73D701075ED15872DDD32BB66D9C /* grdb_config.h */; settings = {ATTRIBUTES = (Project, ); }; };
		A7F8688215DAC06C49979DB5F8EE2628 /* Utils.swift in Sources */ = {isa = PBXBuildFile; fileRef = 428F746DB2BF088721787970C3205D87 /* Utils.swift */; };
		A8E8CE0CA7FD9914124D0056EE9926E1 /* DatabaseFunction.swift in Sources */ = {isa = PBXBuildFile; fileRef = 14E9A84063F394951713F7E568982ECA /* DatabaseFunction.swift */; };
		A94367AE6C7B668A10C87E3A5C6603CC /* SQLExpression.swift in Sources */ = {isa = PBXBuildFile; fileRef = 767700A99BF920BB767D62E48B7151AD /* SQLExpression.swift */; };
		AB51BF708E54DB0B8B69B230F0FD14E5 /* DatabaseReader.swift in Sources */ = {isa = PBXBuildFile; fileRef = A5ECFE7D3A8012D6AB3BE18B43EACEB2 /* DatabaseReader.swift */; };
		AB5CB509F22C0ACB29612F1F34BE3F10 /* Pods-ModieshaAbstract-ModieshaNetworkExtension-umbrella.h in Headers */ = {isa = PBXBuildFile; fileRef = 6A4EAFA201D4B7544FE65A0ABA5E7C9B /* Pods-ModieshaAbstract-ModieshaNetworkExtension-umbrella.h */; settings = {ATTRIBUTES = (Project, ); }; };
		AD03BC4FFEAEB317A322440981DD7E79 /* DatabaseValueConvertible+Encodable.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4D4BB1F0155D4ABEFB2E557C5AC813E6 /* DatabaseValueConvertible+Encodable.swift */; };
		AE6DF63A531C4419EB2126856BF4EFFA /* ReadWriteBox.swift in Sources */ = {isa = PBXBuildFile; fileRef = DB07150F7862C388A3D4C888A05BB390 /* ReadWriteBox.swift */; };
		******************************** /* FTS3+QueryInterface.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1944D8DE5F4AB01AED9960066E2BB480 /* FTS3+QueryInterface.swift */; };
		B0F6DA5F8CC7A0D2B0BBC1FF8A3B9FD0 /* FetchRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4C6A01B557B8B476BFD61AF4AEF029E0 /* FetchRequest.swift */; };
		B1DBF7F0372A52425FE65305D9E93900 /* FTS3.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4FBCE0192803776E24D2DD5C21E603B5 /* FTS3.swift */; };
		B2BE472C1B222D1613D145588223CEFE /* DumpFormat.swift in Sources */ = {isa = PBXBuildFile; fileRef = D55B494AB8BA44925389910907C7E604 /* DumpFormat.swift */; };
		B32B045F01565994FAB625B0BB73ACEB /* Date.swift in Sources */ = {isa = PBXBuildFile; fileRef = F89C5AC99BCEC3351694148501B34BA9 /* Date.swift */; };
		B4AB7A8F20500435056CA49B488FADD6 /* SerializedDatabase.swift in Sources */ = {isa = PBXBuildFile; fileRef = B7FEA070ACA579B706E22ADE2EB533EA /* SerializedDatabase.swift */; };
		B6FEA11600C65EB7B31E8B3EF863434E /* TransactionClock.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8B880C021E3EE9CE1607614CFDEC3554 /* TransactionClock.swift */; };
		B900B923D0744A58D3784DA2C75DE1A1 /* FTS3Pattern.swift in Sources */ = {isa = PBXBuildFile; fileRef = 00B3CEDCD692D616D3C0FB40409CF433 /* FTS3Pattern.swift */; };
		BAB044DA7C5C1BF240E47D648D6DE3FD /* JSONDumpFormat.swift in Sources */ = {isa = PBXBuildFile; fileRef = B5F25F0897539928F5D51BAACE5D2C13 /* JSONDumpFormat.swift */; };
		BB6769D12569C9AAC0F9F0994111972E /* Fixits.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8F786726DD4FF6006E09BF08E2E8A5D9 /* Fixits.swift */; };
		BD18C0573E08E520AA3BC20A42725ED4 /* SQLForeignKeyRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 11889429415470253DB2BFC852D12026 /* SQLForeignKeyRequest.swift */; };
		BE5E634346551CD93D6625C980A310A4 /* TableDefinition.swift in Sources */ = {isa = PBXBuildFile; fileRef = 82A5AB72A281DBA3C388DD1C49C09AF7 /* TableDefinition.swift */; };
		C08F87E122D7F06C907AA749B2D68515 /* DatabaseMigrator.swift in Sources */ = {isa = PBXBuildFile; fileRef = 69366054171AEE0EB896153D9AB0EB8A /* DatabaseMigrator.swift */; };
		C49614D99B691A36496738B94914654B /* Database+Statements.swift in Sources */ = {isa = PBXBuildFile; fileRef = 71F583D8FA7AB2D3A8E023F311093661 /* Database+Statements.swift */; };
		C5668DE51090BD6A95197268E5817567 /* HasManyThroughAssociation.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7AA6B68AD8F5A71CA5F93AE79B77A9C6 /* HasManyThroughAssociation.swift */; };
		C5E69855EF6B369C8C28E4C689CE803D /* Optional.swift in Sources */ = {isa = PBXBuildFile; fileRef = 37895F479324A9121D0B01B6741D3CF2 /* Optional.swift */; };
		C6A0FAA90F8544F3A2280AFEA38696D4 /* JSONColumn.swift in Sources */ = {isa = PBXBuildFile; fileRef = 63F04DD6EF6901CA0AE963B860AD4F14 /* JSONColumn.swift */; };
		C701C33C6B39C2B4D689638C526F3A2E /* Pool.swift in Sources */ = {isa = PBXBuildFile; fileRef = E2AC2D4493E3E72EE49ABA0598CBCBB6 /* Pool.swift */; };
		C8FD6BDAFF4D62033673FA17AF5664C6 /* DatabaseReader+dump.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE1A1D95FE79F83C3F0063454AB40A30 /* DatabaseReader+dump.swift */; };
		C957A922B05F6471CAB45281A4F771F3 /* FTS3TokenizerDescriptor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5ED9D6844541B43D775581DBB3B7CF43 /* FTS3TokenizerDescriptor.swift */; };
		CAEB662257512CC8051BCB4535CEC8DC /* RowDecodingError.swift in Sources */ = {isa = PBXBuildFile; fileRef = 774DE9CC39DB0120A82960BADCCF2163 /* RowDecodingError.swift */; };
		CB9F228B087D60BBAC0572AB2E0899FE /* SQLQueryGenerator.swift in Sources */ = {isa = PBXBuildFile; fileRef = 204AD90BB1D06BB88146588B15ACEE62 /* SQLQueryGenerator.swift */; };
		CCB6D465BA977EFDA2EA3FBA920B8CBE /* DatabaseValue.swift in Sources */ = {isa = PBXBuildFile; fileRef = D0CF28EC5D51661EFE03EF4A32701EA8 /* DatabaseValue.swift */; };
		CCF03CD2F6FAB48507716F17D1C2251D /* Statement.swift in Sources */ = {isa = PBXBuildFile; fileRef = EDB4C17DEBFC19EDE67CFB85A3573A58 /* Statement.swift */; };
		CD46BB1B71339D4CF47B6A4B1DCDA6DD /* DatabasePublishers.swift in Sources */ = {isa = PBXBuildFile; fileRef = 815023D50682D3F2E75DC9FC4188B71A /* DatabasePublishers.swift */; };
		CE8CDF07F22ED0C8C0A70DE4FC119214 /* Decimal.swift in Sources */ = {isa = PBXBuildFile; fileRef = BD4898E489E3D284A2BF14A7A0A4DF78 /* Decimal.swift */; };
		D22E377D0285C71432467B5D320C4AA0 /* FetchableRecord+TableRecord.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A188B469CF426640CE36E6F056F4F0F /* FetchableRecord+TableRecord.swift */; };
		******************************** /* ReceiveValuesOn.swift in Sources */ = {isa = PBXBuildFile; fileRef = ******************************** /* ReceiveValuesOn.swift */; };
		D6267A09CFBC101D3D5071DB46665AAB /* DatabaseWriter.swift in Sources */ = {isa = PBXBuildFile; fileRef = D0AF446995D61FF0AD7F9DBF7CB1287F /* DatabaseWriter.swift */; };
		D89261C389582DF3658F98B4341D2068 /* SQLIndexGenerator.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2AE829E4AD6B67D662C903E433969FFB /* SQLIndexGenerator.swift */; };
		D967C68ABAD9608D9795FCED447FB4BF /* TableRecord+QueryInterfaceRequest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 18128178E98F852DC1E50E60F456DC76 /* TableRecord+QueryInterfaceRequest.swift */; };
		D9BA53490093CD9F1833CE3F0C972E4C /* DatabaseDateComponents.swift in Sources */ = {isa = PBXBuildFile; fileRef = 04B476B49DBFBC46E7EA7E75DBAF8EBA /* DatabaseDateComponents.swift */; };
		DA1A0291468B6C4774DAC5CA51DA26B9 /* ValueConcurrentObserver.swift in Sources */ = {isa = PBXBuildFile; fileRef = C3714481AE3A0ED65AF8FB726F4CB489 /* ValueConcurrentObserver.swift */; };
		DB868D713731EED3B360B53D1E823F7B /* Column.swift in Sources */ = {isa = PBXBuildFile; fileRef = 51CCC9B7D4F7368C2FCBF41E18145CDD /* Column.swift */; };
		DB9BC9316B737AE30C54408BF2912E86 /* ForeignKey.swift in Sources */ = {isa = PBXBuildFile; fileRef = A5B56609D6212E5B0A1DB61A189F2721 /* ForeignKey.swift */; };
		DC73C142BAFFA8B25315E39C6266F271 /* LineDumpFormat.swift in Sources */ = {isa = PBXBuildFile; fileRef = 77019093D9B2F9A4E2A91AED17808715 /* LineDumpFormat.swift */; };
		DE01D3E37CE04761FC7DE1308C39FF29 /* RequestProtocols.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8D0C41B09B5F6902CCD1B232932DD6B4 /* RequestProtocols.swift */; };
		E15158C49D0DD7EB3371F7CC91C10236 /* Migration.swift in Sources */ = {isa = PBXBuildFile; fileRef = E954404A7324673BD1A7C655BABF7027 /* Migration.swift */; };
		E3DC8078E3B7426333D874068DFC1575 /* MutablePersistableRecord+Update.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4551E2AED0594503189C472ABB31E179 /* MutablePersistableRecord+Update.swift */; };
		E567B2CB5242CD050705D9BB678C3F4A /* SQLSelection.swift in Sources */ = {isa = PBXBuildFile; fileRef = C2D2EF97F6E59A863A8E6204EED85ECB /* SQLSelection.swift */; };
		E5DD4F4F54F2FE80D09A870945C8FF05 /* DatabaseValueConvertible+ReferenceConvertible.swift in Sources */ = {isa = PBXBuildFile; fileRef = 13A6F2F013641B994EF8AEADF76C734C /* DatabaseValueConvertible+ReferenceConvertible.swift */; };
		E6D79E22993B1B75F3DF19CDAA2161C6 /* SQLInterpolation+QueryInterface.swift in Sources */ = {isa = PBXBuildFile; fileRef = D1962053DFB78C1C8168956E4DE61E6B /* SQLInterpolation+QueryInterface.swift */; };
		E97202A7B5B9D4C1F5C696C829D91161 /* IndexDefinition.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3F1BDADBB6D45319B3435D9C67A20AEC /* IndexDefinition.swift */; };
		EB0E6259B434D8A6E2070D8A7356360B /* StatementAuthorizer.swift in Sources */ = {isa = PBXBuildFile; fileRef = D37EF498226DF3994651669AC231DCB3 /* StatementAuthorizer.swift */; };
		EC131593FA475D137D0EEDEAD55C0C7B /* StandardLibrary.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4A29E380EFA520EE44DAB0B63CE3BEFC /* StandardLibrary.swift */; };
		EC9810D6FA92C63FD0ECAA1CD6798E87 /* WALSnapshotTransaction.swift in Sources */ = {isa = PBXBuildFile; fileRef = 198CD5B477C15883281A81E554D6F078 /* WALSnapshotTransaction.swift */; };
		F0A27163430D8BC84BBA3379C05F332A /* Cursor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8218E7169BCBEDED85E4AAE2FEB29C93 /* Cursor.swift */; };
		F0F949EA9B0BC2294C1B11AF82EEF54D /* Refinable.swift in Sources */ = {isa = PBXBuildFile; fileRef = 956960709BE3F74DFD182B9D341064B6 /* Refinable.swift */; };
		F14F594508B24B722CEAD4706842D769 /* DatabaseRegion.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8A6B565106F35F08E99767A3BD0E665B /* DatabaseRegion.swift */; };
		F1A40189ED776C0C17311545E8283D9B /* DatabaseValueConvertible+RawRepresentable.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9BC7D6668E0B972343963419EEB336C1 /* DatabaseValueConvertible+RawRepresentable.swift */; };
		F3406678A1B9EAFCAAB07F4D72446BA0 /* MutablePersistableRecord+Delete.swift in Sources */ = {isa = PBXBuildFile; fileRef = CC245AE6D66EDBB5D52180C585361F9D /* MutablePersistableRecord+Delete.swift */; };
		F3BCE49BBFCA025AB8D2CE3225B2D024 /* DatabaseBackupProgress.swift in Sources */ = {isa = PBXBuildFile; fileRef = 554DA53556F07D4619BC8FC653FE1D94 /* DatabaseBackupProgress.swift */; };
		F5615C419817350F24FAF9F6B6438156 /* MutablePersistableRecord.swift in Sources */ = {isa = PBXBuildFile; fileRef = 720A913243FE97B3F4397E93FF358607 /* MutablePersistableRecord.swift */; };
		F7D480C5BE155827A6D088F17C50173F /* FetchableRecord+Decodable.swift in Sources */ = {isa = PBXBuildFile; fileRef = CED33B3F922AF61214020F84684AF704 /* FetchableRecord+Decodable.swift */; };
		F984A7A0AE4D5CE9B3E96ED9A752ED77 /* Pods-ModieshaAbstract-Modiesha-dummy.m in Sources */ = {isa = PBXBuildFile; fileRef = 19A4A88B2BA859AD5F2EA19DA487AB1E /* Pods-ModieshaAbstract-Modiesha-dummy.m */; };
		FB62B1ACDD2DDEB9BE1E1353FA44DB2F /* SQLRelation.swift in Sources */ = {isa = PBXBuildFile; fileRef = F81894840B7CB79E0671C18610C6FDD3 /* SQLRelation.swift */; };
		FB7E24EDF342A87947A9384E030C9D91 /* TableAlteration.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8A8B65ABA4C06E27438DB5ABBA6EAE48 /* TableAlteration.swift */; };
		FBCAEAFBF3A5462C5B6E93247C4D1E9A /* Association.swift in Sources */ = {isa = PBXBuildFile; fileRef = ******************************** /* Association.swift */; };
		FF1BDBD29C9ED3EC0ED58A71A98430F1 /* NSString.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8A77688DC09228793560E7C23A2D8F71 /* NSString.swift */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		179987DA720CD28449502CE854BD5062 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 0A86C228CFCBC35A1FD284274ED0930F;
			remoteInfo = GRDB.swift;
		};
		86CD9CD4EFD21F56EF67E5D2FC51C274 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BFDFE7DC352907FC980B868725387E98 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 0A86C228CFCBC35A1FD284274ED0930F;
			remoteInfo = GRDB.swift;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		00B3CEDCD692D616D3C0FB40409CF433 /* FTS3Pattern.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = FTS3Pattern.swift; path = GRDB/FTS/FTS3Pattern.swift; sourceTree = "<group>"; };
		016E2061B097BFA66B10478D091044BA /* FetchableRecord.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = FetchableRecord.swift; path = GRDB/Record/FetchableRecord.swift; sourceTree = "<group>"; };
		021AA890A0DCF0117F99A82A7887B50D /* EncodableRecord.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = EncodableRecord.swift; path = GRDB/Record/EncodableRecord.swift; sourceTree = "<group>"; };
		04B476B49DBFBC46E7EA7E75DBAF8EBA /* DatabaseDateComponents.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DatabaseDateComponents.swift; path = GRDB/Core/Support/Foundation/DatabaseDateComponents.swift; sourceTree = "<group>"; };
		05C0CF7729B3BD44508725A598A967DC /* RemoveDuplicates.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = RemoveDuplicates.swift; path = GRDB/ValueObservation/Reducers/RemoveDuplicates.swift; sourceTree = "<group>"; };
		0620725A7D069E3ED5B5F55F5B944676 /* DatabaseQueue.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DatabaseQueue.swift; path = GRDB/Core/DatabaseQueue.swift; sourceTree = "<group>"; };
		07B47A9C3E533110DD7F5AE25761EB1A /* Database+SchemaDefinition.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "Database+SchemaDefinition.swift"; path = "GRDB/QueryInterface/Schema/Database+SchemaDefinition.swift"; sourceTree = "<group>"; };
		096B73D701075ED15872DDD32BB66D9C /* grdb_config.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; name = grdb_config.h; path = Support/grdb_config.h; sourceTree = "<group>"; };
		0B14E141F9E8D628BFE364458061CE6B /* GRDB.swift.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = GRDB.swift.debug.xcconfig; sourceTree = "<group>"; };
		0C453FC093572099C191ED405BFF4138 /* WALSnapshot.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = WALSnapshot.swift; path = GRDB/Core/WALSnapshot.swift; sourceTree = "<group>"; };
		0DF5A81B026D5BE13D40FFF53710314B /* FTS5CustomTokenizer.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = FTS5CustomTokenizer.swift; path = GRDB/FTS/FTS5CustomTokenizer.swift; sourceTree = "<group>"; };
		1009E22874D53C33546C454BBB3CD884 /* SQLFunctions.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = SQLFunctions.swift; path = GRDB/QueryInterface/SQL/SQLFunctions.swift; sourceTree = "<group>"; };
		10FC594F337E9FEB48BBBC8618B8D7DE /* SQL.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = SQL.swift; path = GRDB/Core/SQL.swift; sourceTree = "<group>"; };
		11852E03C78BAB0865A1D8726B5B4EFD /* DatabaseSnapshot.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DatabaseSnapshot.swift; path = GRDB/Core/DatabaseSnapshot.swift; sourceTree = "<group>"; };
		11889429415470253DB2BFC852D12026 /* SQLForeignKeyRequest.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = SQLForeignKeyRequest.swift; path = GRDB/QueryInterface/SQL/SQLForeignKeyRequest.swift; sourceTree = "<group>"; };
		13A6F2F013641B994EF8AEADF76C734C /* DatabaseValueConvertible+ReferenceConvertible.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "DatabaseValueConvertible+ReferenceConvertible.swift"; path = "GRDB/Core/Support/Foundation/DatabaseValueConvertible+ReferenceConvertible.swift"; sourceTree = "<group>"; };
		14DF23AA4CE2CB94DCEE4BC4BFAD8FA8 /* MutablePersistableRecord+Upsert.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "MutablePersistableRecord+Upsert.swift"; path = "GRDB/Record/MutablePersistableRecord+Upsert.swift"; sourceTree = "<group>"; };
		14E9A84063F394951713F7E568982ECA /* DatabaseFunction.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DatabaseFunction.swift; path = GRDB/Core/DatabaseFunction.swift; sourceTree = "<group>"; };
		1583CC47BEDD1F7693E8531540F8B9AA /* Database.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Database.swift; path = GRDB/Core/Database.swift; sourceTree = "<group>"; };
		1736B1EAEB34F5BF2DD1EFE8947280BE /* SQLRequest.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = SQLRequest.swift; path = GRDB/Core/SQLRequest.swift; sourceTree = "<group>"; };
		17ABD68E920293F080122810A8881638 /* GRDB.swift */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; name = GRDB.swift; path = libGRDB.swift.a; sourceTree = BUILT_PRODUCTS_DIR; };
		******************************** /* Map.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Map.swift; path = GRDB/ValueObservation/Reducers/Map.swift; sourceTree = "<group>"; };
		18128178E98F852DC1E50E60F456DC76 /* TableRecord+QueryInterfaceRequest.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "TableRecord+QueryInterfaceRequest.swift"; path = "GRDB/QueryInterface/TableRecord+QueryInterfaceRequest.swift"; sourceTree = "<group>"; };
		1944D8DE5F4AB01AED9960066E2BB480 /* FTS3+QueryInterface.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "FTS3+QueryInterface.swift"; path = "GRDB/QueryInterface/FTS3+QueryInterface.swift"; sourceTree = "<group>"; };
		198CD5B477C15883281A81E554D6F078 /* WALSnapshotTransaction.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = WALSnapshotTransaction.swift; path = GRDB/Core/WALSnapshotTransaction.swift; sourceTree = "<group>"; };
		19A4A88B2BA859AD5F2EA19DA487AB1E /* Pods-ModieshaAbstract-Modiesha-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "Pods-ModieshaAbstract-Modiesha-dummy.m"; sourceTree = "<group>"; };
		1A188B469CF426640CE36E6F056F4F0F /* FetchableRecord+TableRecord.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "FetchableRecord+TableRecord.swift"; path = "GRDB/Record/FetchableRecord+TableRecord.swift"; sourceTree = "<group>"; };
		1D0E0177B1367FD207DD0768F52E0BA8 /* Inflections+English.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "Inflections+English.swift"; path = "GRDB/Utils/Inflections+English.swift"; sourceTree = "<group>"; };
		204AD90BB1D06BB88146588B15ACEE62 /* SQLQueryGenerator.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = SQLQueryGenerator.swift; path = GRDB/QueryInterface/SQLGeneration/SQLQueryGenerator.swift; sourceTree = "<group>"; };
		22334CC0DC7B4F46313D8CDB3F7F28A0 /* FTS5Pattern.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = FTS5Pattern.swift; path = GRDB/FTS/FTS5Pattern.swift; sourceTree = "<group>"; };
		2304C1763E945E54C3C28E9485174327 /* SQLiteDateParser.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = SQLiteDateParser.swift; path = GRDB/Core/Support/Foundation/SQLiteDateParser.swift; sourceTree = "<group>"; };
		23DF3F34024629A7EFDCA05A285A53BF /* EncodableRecord+Encodable.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "EncodableRecord+Encodable.swift"; path = "GRDB/Record/EncodableRecord+Encodable.swift"; sourceTree = "<group>"; };
		27845748D29D2173E454A48D2FF7A17F /* Pods-ModieshaAbstract-Modiesha.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-ModieshaAbstract-Modiesha.debug.xcconfig"; sourceTree = "<group>"; };
		2AE829E4AD6B67D662C903E433969FFB /* SQLIndexGenerator.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = SQLIndexGenerator.swift; path = GRDB/QueryInterface/SQLGeneration/SQLIndexGenerator.swift; sourceTree = "<group>"; };
		2C99D87D1E10E59A4013A4EE24A752D9 /* DatabaseCancellable.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DatabaseCancellable.swift; path = GRDB/ValueObservation/DatabaseCancellable.swift; sourceTree = "<group>"; };
		2D0614CE22C1D5EB335D8998F907F883 /* DatabaseRegionObservation.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DatabaseRegionObservation.swift; path = GRDB/Core/DatabaseRegionObservation.swift; sourceTree = "<group>"; };
		2F5E2B9AA26CC514E436FEEDF709C9E7 /* VirtualTableModule.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = VirtualTableModule.swift; path = GRDB/QueryInterface/Schema/VirtualTableModule.swift; sourceTree = "<group>"; };
		31A3311B39CA89447090A260F98A374B /* QueryInterfaceRequest.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = QueryInterfaceRequest.swift; path = GRDB/QueryInterface/Request/QueryInterfaceRequest.swift; sourceTree = "<group>"; };
		321E71AC72C7C068240A76EF7231C33C /* DatabaseSchemaCache.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DatabaseSchemaCache.swift; path = GRDB/Core/DatabaseSchemaCache.swift; sourceTree = "<group>"; };
		32F6C3E5C32949204F620A4469D31F2B /* Pods-ModieshaAbstract-Modiesha-acknowledgements.markdown */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text; path = "Pods-ModieshaAbstract-Modiesha-acknowledgements.markdown"; sourceTree = "<group>"; };
		37895F479324A9121D0B01B6741D3CF2 /* Optional.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Optional.swift; path = GRDB/Core/Support/StandardLibrary/Optional.swift; sourceTree = "<group>"; };
		37BF8404677F899C6C30BAA59253D42F /* CommonTableExpression.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CommonTableExpression.swift; path = GRDB/QueryInterface/Request/CommonTableExpression.swift; sourceTree = "<group>"; };
		388B2F1CD028B8F3574599CCD6A9FDD2 /* FTS5WrapperTokenizer.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = FTS5WrapperTokenizer.swift; path = GRDB/FTS/FTS5WrapperTokenizer.swift; sourceTree = "<group>"; };
		38DEDC908B46EDAB870C1F79C5DE3E73 /* LockedBox.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = LockedBox.swift; path = GRDB/Utils/LockedBox.swift; sourceTree = "<group>"; };
		******************************** /* Association.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Association.swift; path = GRDB/QueryInterface/Request/Association/Association.swift; sourceTree = "<group>"; };
		3ABA6321FEF4A2863DEB4671D2D082A4 /* DebugDumpFormat.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DebugDumpFormat.swift; path = GRDB/Dump/DumpFormats/DebugDumpFormat.swift; sourceTree = "<group>"; };
		3F1BDADBB6D45319B3435D9C67A20AEC /* IndexDefinition.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = IndexDefinition.swift; path = GRDB/QueryInterface/Schema/IndexDefinition.swift; sourceTree = "<group>"; };
		40B6FC2033DD5642D2B395CF9AC509ED /* DatabaseValueConvertible.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DatabaseValueConvertible.swift; path = GRDB/Core/DatabaseValueConvertible.swift; sourceTree = "<group>"; };
		4178530D5612259DA2FA3F3858DD4F12 /* PersistableRecord+Save.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "PersistableRecord+Save.swift"; path = "GRDB/Record/PersistableRecord+Save.swift"; sourceTree = "<group>"; };
		428F746DB2BF088721787970C3205D87 /* Utils.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Utils.swift; path = GRDB/Utils/Utils.swift; sourceTree = "<group>"; };
		44216A4D9279269162BD9014BE0AAED4 /* SQLSubquery.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = SQLSubquery.swift; path = GRDB/QueryInterface/SQL/SQLSubquery.swift; sourceTree = "<group>"; };
		4551E2AED0594503189C472ABB31E179 /* MutablePersistableRecord+Update.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "MutablePersistableRecord+Update.swift"; path = "GRDB/Record/MutablePersistableRecord+Update.swift"; sourceTree = "<group>"; };
		47EBFCC60F1F01C8D8599DEB8653799C /* TableRecord.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = TableRecord.swift; path = GRDB/Record/TableRecord.swift; sourceTree = "<group>"; };
		494A688706416F6AF24F7BD863013859 /* Pods-ModieshaAbstract-ModieshaNetworkExtension-acknowledgements.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-ModieshaAbstract-ModieshaNetworkExtension-acknowledgements.plist"; sourceTree = "<group>"; };
		4A29E380EFA520EE44DAB0B63CE3BEFC /* StandardLibrary.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = StandardLibrary.swift; path = GRDB/Core/Support/StandardLibrary/StandardLibrary.swift; sourceTree = "<group>"; };
		4A7F10CADD50929EF5A5362CB7CFD322 /* SQLGenerationContext.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = SQLGenerationContext.swift; path = GRDB/QueryInterface/SQLGeneration/SQLGenerationContext.swift; sourceTree = "<group>"; };
		4B543A3EB29924C2EBC9F92CCE7D9151 /* GRDB.swift-prefix.pch */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "GRDB.swift-prefix.pch"; sourceTree = "<group>"; };
		4C6A01B557B8B476BFD61AF4AEF029E0 /* FetchRequest.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = FetchRequest.swift; path = GRDB/Core/FetchRequest.swift; sourceTree = "<group>"; };
		4D4BB1F0155D4ABEFB2E557C5AC813E6 /* DatabaseValueConvertible+Encodable.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "DatabaseValueConvertible+Encodable.swift"; path = "GRDB/Core/Support/StandardLibrary/DatabaseValueConvertible+Encodable.swift"; sourceTree = "<group>"; };
		4FBCE0192803776E24D2DD5C21E603B5 /* FTS3.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = FTS3.swift; path = GRDB/FTS/FTS3.swift; sourceTree = "<group>"; };
		51134571D7BE49B887CB0FACEB350D7F /* Pods-ModieshaAbstract-ModieshaNetworkExtension-acknowledgements.markdown */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text; path = "Pods-ModieshaAbstract-ModieshaNetworkExtension-acknowledgements.markdown"; sourceTree = "<group>"; };
		51C4EB813981C04D0308AEE260A4327C /* DatabaseError.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DatabaseError.swift; path = GRDB/Core/DatabaseError.swift; sourceTree = "<group>"; };
		51CCC9B7D4F7368C2FCBF41E18145CDD /* Column.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Column.swift; path = GRDB/QueryInterface/SQL/Column.swift; sourceTree = "<group>"; };
		5311AD1A0301473E6E953DBB50802600 /* NSNull.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = NSNull.swift; path = GRDB/Core/Support/Foundation/NSNull.swift; sourceTree = "<group>"; };
		554DA53556F07D4619BC8FC653FE1D94 /* DatabaseBackupProgress.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DatabaseBackupProgress.swift; path = GRDB/Core/DatabaseBackupProgress.swift; sourceTree = "<group>"; };
		555916DFDA1773EBAD92C5E930EC715C /* Pods-ModieshaAbstract-Modiesha-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "Pods-ModieshaAbstract-Modiesha-umbrella.h"; sourceTree = "<group>"; };
		5DFB326A42D7B015A8C880684606F0B5 /* Pods-ModieshaAbstract-Modiesha.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = "Pods-ModieshaAbstract-Modiesha.modulemap"; sourceTree = "<group>"; };
		5ED9D6844541B43D775581DBB3B7CF43 /* FTS3TokenizerDescriptor.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = FTS3TokenizerDescriptor.swift; path = GRDB/FTS/FTS3TokenizerDescriptor.swift; sourceTree = "<group>"; };
		61263E84786D15358777F9F2CC0E7D03 /* JoinAssociation.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = JoinAssociation.swift; path = GRDB/QueryInterface/Request/Association/JoinAssociation.swift; sourceTree = "<group>"; };
		615EB3F81068E456BBD195FFEA5E4328 /* Database+Schema.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "Database+Schema.swift"; path = "GRDB/Core/Database+Schema.swift"; sourceTree = "<group>"; };
		6183D4280DF1812C2E42BC14C99E66B9 /* ValueReducer.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ValueReducer.swift; path = GRDB/ValueObservation/Reducers/ValueReducer.swift; sourceTree = "<group>"; };
		62D99F0E489DAFFB3BBC32D8714D2627 /* DatabaseSnapshotPool.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DatabaseSnapshotPool.swift; path = GRDB/Core/DatabaseSnapshotPool.swift; sourceTree = "<group>"; };
		63F04DD6EF6901CA0AE963B860AD4F14 /* JSONColumn.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = JSONColumn.swift; path = GRDB/JSON/JSONColumn.swift; sourceTree = "<group>"; };
		66F728148ADC5662C8ED900A2BA5BFB5 /* Trace.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Trace.swift; path = GRDB/ValueObservation/Reducers/Trace.swift; sourceTree = "<group>"; };
		68A89820B42F8589FAF2340F29D31BED /* ValueWriteOnlyObserver.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ValueWriteOnlyObserver.swift; path = GRDB/ValueObservation/Observers/ValueWriteOnlyObserver.swift; sourceTree = "<group>"; };
		69366054171AEE0EB896153D9AB0EB8A /* DatabaseMigrator.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DatabaseMigrator.swift; path = GRDB/Migration/DatabaseMigrator.swift; sourceTree = "<group>"; };
		6A262FB4924F5CD64B55DC036317EE28 /* SQLJSONExpressible.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = SQLJSONExpressible.swift; path = GRDB/JSON/SQLJSONExpressible.swift; sourceTree = "<group>"; };
		6A4EAFA201D4B7544FE65A0ABA5E7C9B /* Pods-ModieshaAbstract-ModieshaNetworkExtension-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "Pods-ModieshaAbstract-ModieshaNetworkExtension-umbrella.h"; sourceTree = "<group>"; };
		6B9AD445394F39E5D2A69A543FF8BC8D /* Table.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Table.swift; path = GRDB/QueryInterface/SQL/Table.swift; sourceTree = "<group>"; };
		6D4980C3E9F28FCB22C50249D9075068 /* ForeignKeyDefinition.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ForeignKeyDefinition.swift; path = GRDB/QueryInterface/Schema/ForeignKeyDefinition.swift; sourceTree = "<group>"; };
		6F4883C0CAAB145A8CF3D790831C78E5 /* PersistableRecord+Insert.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "PersistableRecord+Insert.swift"; path = "GRDB/Record/PersistableRecord+Insert.swift"; sourceTree = "<group>"; };
		71F583D8FA7AB2D3A8E023F311093661 /* Database+Statements.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "Database+Statements.swift"; path = "GRDB/Core/Database+Statements.swift"; sourceTree = "<group>"; };
		720A913243FE97B3F4397E93FF358607 /* MutablePersistableRecord.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = MutablePersistableRecord.swift; path = GRDB/Record/MutablePersistableRecord.swift; sourceTree = "<group>"; };
		767700A99BF920BB767D62E48B7151AD /* SQLExpression.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = SQLExpression.swift; path = GRDB/QueryInterface/SQL/SQLExpression.swift; sourceTree = "<group>"; };
		76B279BAEC64F976C831791D9F6084EF /* FTS5TokenizerDescriptor.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = FTS5TokenizerDescriptor.swift; path = GRDB/FTS/FTS5TokenizerDescriptor.swift; sourceTree = "<group>"; };
		77019093D9B2F9A4E2A91AED17808715 /* LineDumpFormat.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = LineDumpFormat.swift; path = GRDB/Dump/DumpFormats/LineDumpFormat.swift; sourceTree = "<group>"; };
		774DE9CC39DB0120A82960BADCCF2163 /* RowDecodingError.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = RowDecodingError.swift; path = GRDB/Core/RowDecodingError.swift; sourceTree = "<group>"; };
		777A27CF6277B7EFD3D287F10AD06B45 /* GRDB.swift.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = GRDB.swift.modulemap; sourceTree = "<group>"; };
		78C0D1A3B8E52B4A817276B7F7F78A7B /* MutablePersistableRecord+Insert.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "MutablePersistableRecord+Insert.swift"; path = "GRDB/Record/MutablePersistableRecord+Insert.swift"; sourceTree = "<group>"; };
		7A17806744DF62AE8CFA4B9C24679F45 /* Pods-ModieshaAbstract-ModieshaNetworkExtension */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; name = "Pods-ModieshaAbstract-ModieshaNetworkExtension"; path = "libPods-ModieshaAbstract-ModieshaNetworkExtension.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		7AA6B68AD8F5A71CA5F93AE79B77A9C6 /* HasManyThroughAssociation.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = HasManyThroughAssociation.swift; path = GRDB/QueryInterface/Request/Association/HasManyThroughAssociation.swift; sourceTree = "<group>"; };
		7BF85F6F2ACEABEEFACBC4CA17B56085 /* Record.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Record.swift; path = GRDB/Record/Record.swift; sourceTree = "<group>"; };
		7D64A2B2338483128C97F43E6458CF6E /* Configuration.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Configuration.swift; path = GRDB/Core/Configuration.swift; sourceTree = "<group>"; };
		7F05BE80FB8AD9271DA034909154A81B /* DatabaseValueConvertible+Decodable.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "DatabaseValueConvertible+Decodable.swift"; path = "GRDB/Core/Support/StandardLibrary/DatabaseValueConvertible+Decodable.swift"; sourceTree = "<group>"; };
		7FBAB9476E2C3CB744FC7DA2E200F4EB /* SharedValueObservation.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = SharedValueObservation.swift; path = GRDB/ValueObservation/SharedValueObservation.swift; sourceTree = "<group>"; };
		815023D50682D3F2E75DC9FC4188B71A /* DatabasePublishers.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DatabasePublishers.swift; path = GRDB/Core/DatabasePublishers.swift; sourceTree = "<group>"; };
		8218E7169BCBEDED85E4AAE2FEB29C93 /* Cursor.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Cursor.swift; path = GRDB/Core/Cursor.swift; sourceTree = "<group>"; };
		82A5AB72A281DBA3C388DD1C49C09AF7 /* TableDefinition.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = TableDefinition.swift; path = GRDB/QueryInterface/Schema/TableDefinition.swift; sourceTree = "<group>"; };
		82B0B7B5D11A20DBE23140364BEC3DD4 /* Data.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Data.swift; path = GRDB/Core/Support/Foundation/Data.swift; sourceTree = "<group>"; };
		8542EB95445579DC0C67C29B19FFCC7A /* SQLColumnGenerator.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = SQLColumnGenerator.swift; path = GRDB/QueryInterface/SQLGeneration/SQLColumnGenerator.swift; sourceTree = "<group>"; };
		87EE6FB45D98A10D5F2C1B32DE597EA7 /* HasOneAssociation.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = HasOneAssociation.swift; path = GRDB/QueryInterface/Request/Association/HasOneAssociation.swift; sourceTree = "<group>"; };
		87FE65AA0EDEB1E83CB67C338E1B33D6 /* OnDemandFuture.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = OnDemandFuture.swift; path = GRDB/Utils/OnDemandFuture.swift; sourceTree = "<group>"; };
		897F3FC97B2081FC917411BF4590D32C /* DatabaseCollation.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DatabaseCollation.swift; path = GRDB/Core/DatabaseCollation.swift; sourceTree = "<group>"; };
		8A6B565106F35F08E99767A3BD0E665B /* DatabaseRegion.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DatabaseRegion.swift; path = GRDB/Core/DatabaseRegion.swift; sourceTree = "<group>"; };
		8A77688DC09228793560E7C23A2D8F71 /* NSString.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = NSString.swift; path = GRDB/Core/Support/Foundation/NSString.swift; sourceTree = "<group>"; };
		8A8B65ABA4C06E27438DB5ABBA6EAE48 /* TableAlteration.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = TableAlteration.swift; path = GRDB/QueryInterface/Schema/TableAlteration.swift; sourceTree = "<group>"; };
		8A91D0A9DDA70D06730E8AE4B2F0DB74 /* PersistableRecord.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = PersistableRecord.swift; path = GRDB/Record/PersistableRecord.swift; sourceTree = "<group>"; };
		8B880C021E3EE9CE1607614CFDEC3554 /* TransactionClock.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = TransactionClock.swift; path = GRDB/Core/TransactionClock.swift; sourceTree = "<group>"; };
		8D0C41B09B5F6902CCD1B232932DD6B4 /* RequestProtocols.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = RequestProtocols.swift; path = GRDB/QueryInterface/Request/RequestProtocols.swift; sourceTree = "<group>"; };
		8F786726DD4FF6006E09BF08E2E8A5D9 /* Fixits.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Fixits.swift; path = GRDB/Fixits.swift; sourceTree = "<group>"; };
		93A6705E6BABC9E73591FBA1D15EA9D0 /* Pods-ModieshaAbstract-ModieshaNetworkExtension-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "Pods-ModieshaAbstract-ModieshaNetworkExtension-dummy.m"; sourceTree = "<group>"; };
		950B9CA657F2617EDED101A99F3EE5C7 /* HasOneThroughAssociation.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = HasOneThroughAssociation.swift; path = GRDB/QueryInterface/Request/Association/HasOneThroughAssociation.swift; sourceTree = "<group>"; };
		956960709BE3F74DFD182B9D341064B6 /* Refinable.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Refinable.swift; path = GRDB/Utils/Refinable.swift; sourceTree = "<group>"; };
		96DEFF6D3ECE2C89F1120C47CCC6BF0C /* Database+Dump.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "Database+Dump.swift"; path = "GRDB/Dump/Database+Dump.swift"; sourceTree = "<group>"; };
		9B7A1B09EF673371DB70CE9BD8F598D8 /* ColumnDefinition.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ColumnDefinition.swift; path = GRDB/QueryInterface/Schema/ColumnDefinition.swift; sourceTree = "<group>"; };
		9BC7D6668E0B972343963419EEB336C1 /* DatabaseValueConvertible+RawRepresentable.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "DatabaseValueConvertible+RawRepresentable.swift"; path = "GRDB/Core/Support/StandardLibrary/DatabaseValueConvertible+RawRepresentable.swift"; sourceTree = "<group>"; };
		9C6963559C18640F4D700BF51B4C1378 /* ValueObservation.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ValueObservation.swift; path = GRDB/ValueObservation/ValueObservation.swift; sourceTree = "<group>"; };
		9D940727FF8FB9C785EB98E56350EF41 /* Podfile */ = {isa = PBXFileReference; explicitFileType = text.script.ruby; includeInIndex = 1; indentWidth = 2; lastKnownFileType = text; name = Podfile; path = ../Podfile; sourceTree = SOURCE_ROOT; tabWidth = 2; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		9F1EBC3B29513F987DF553C66CCD7F26 /* HasManyAssociation.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = HasManyAssociation.swift; path = GRDB/QueryInterface/Request/Association/HasManyAssociation.swift; sourceTree = "<group>"; };
		9FFD36A4AD5BD3EF8C77F20C09B4D253 /* SchedulingWatchdog.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = SchedulingWatchdog.swift; path = GRDB/Core/SchedulingWatchdog.swift; sourceTree = "<group>"; };
		A147E080D60A6E236499B168CBF67FE4 /* Pods-ModieshaAbstract-Modiesha */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; name = "Pods-ModieshaAbstract-Modiesha"; path = "libPods-ModieshaAbstract-Modiesha.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		A50E9AEBBC53BE59D698CB96E9356768 /* ListDumpFormat.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ListDumpFormat.swift; path = GRDB/Dump/DumpFormats/ListDumpFormat.swift; sourceTree = "<group>"; };
		A5B56609D6212E5B0A1DB61A189F2721 /* ForeignKey.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ForeignKey.swift; path = GRDB/QueryInterface/ForeignKey.swift; sourceTree = "<group>"; };
		A5ECFE7D3A8012D6AB3BE18B43EACEB2 /* DatabaseReader.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DatabaseReader.swift; path = GRDB/Core/DatabaseReader.swift; sourceTree = "<group>"; };
		A643D95E49C6F2F7A65F06B75CEF72EB /* SQLAssociation.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = SQLAssociation.swift; path = GRDB/QueryInterface/SQL/SQLAssociation.swift; sourceTree = "<group>"; };
		AAEFDB9140E8D7FC5EA25E00A0EBB8B4 /* PersistableRecord+Upsert.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "PersistableRecord+Upsert.swift"; path = "GRDB/Record/PersistableRecord+Upsert.swift"; sourceTree = "<group>"; };
		AB918EBFAC48537D25DE47CC7836F66B /* SQLTableAlterationGenerator.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = SQLTableAlterationGenerator.swift; path = GRDB/QueryInterface/SQLGeneration/SQLTableAlterationGenerator.swift; sourceTree = "<group>"; };
		ABFF93A854CFE71C275D09ED847B98DC /* SQLOperators.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = SQLOperators.swift; path = GRDB/QueryInterface/SQL/SQLOperators.swift; sourceTree = "<group>"; };
		AC6F69A431347A923AE32E711AF1036B /* BelongsToAssociation.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = BelongsToAssociation.swift; path = GRDB/QueryInterface/Request/Association/BelongsToAssociation.swift; sourceTree = "<group>"; };
		AC809412C67409D65F131ACA3C1C9005 /* UUID.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = UUID.swift; path = GRDB/Core/Support/Foundation/UUID.swift; sourceTree = "<group>"; };
		ACBD16187D363B104ABB2B7C22322048 /* JSONRequiredEncoder.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = JSONRequiredEncoder.swift; path = GRDB/Core/Support/StandardLibrary/JSONRequiredEncoder.swift; sourceTree = "<group>"; };
		AE39C1D9B5E717C79FF3602886448E3F /* MutablePersistableRecord+Save.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "MutablePersistableRecord+Save.swift"; path = "GRDB/Record/MutablePersistableRecord+Save.swift"; sourceTree = "<group>"; };
		B15331ECAA2DB0EE8B7F026273361A37 /* AssociationAggregate.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = AssociationAggregate.swift; path = GRDB/QueryInterface/Request/Association/AssociationAggregate.swift; sourceTree = "<group>"; };
		B31256FFA058F94343D3C656F2FEF4FC /* CGFloat.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CGFloat.swift; path = GRDB/Core/Support/CoreGraphics/CGFloat.swift; sourceTree = "<group>"; };
		B5F25F0897539928F5D51BAACE5D2C13 /* JSONDumpFormat.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = JSONDumpFormat.swift; path = GRDB/Dump/DumpFormats/JSONDumpFormat.swift; sourceTree = "<group>"; };
		B6CB1A3E4360B70F92D669B3856B0CC7 /* GRDB.swift-umbrella.h */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.h; path = "GRDB.swift-umbrella.h"; sourceTree = "<group>"; };
		B7FEA070ACA579B706E22ADE2EB533EA /* SerializedDatabase.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = SerializedDatabase.swift; path = GRDB/Core/SerializedDatabase.swift; sourceTree = "<group>"; };
		B86DB54C8B71DBBF943081C7BC174D26 /* StatementColumnConvertible.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = StatementColumnConvertible.swift; path = GRDB/Core/StatementColumnConvertible.swift; sourceTree = "<group>"; };
		B974416EDA6FEBE3C61AA4BC253913AA /* Pods-ModieshaAbstract-Modiesha.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-ModieshaAbstract-Modiesha.release.xcconfig"; sourceTree = "<group>"; };
		BC35C2AF4BB8A01B2ADCD4812F3115E4 /* SQLInterpolation.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = SQLInterpolation.swift; path = GRDB/Core/SQLInterpolation.swift; sourceTree = "<group>"; };
		BD4898E489E3D284A2BF14A7A0A4DF78 /* Decimal.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Decimal.swift; path = GRDB/Core/Support/Foundation/Decimal.swift; sourceTree = "<group>"; };
		BE1A1D95FE79F83C3F0063454AB40A30 /* DatabaseReader+dump.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "DatabaseReader+dump.swift"; path = "GRDB/Dump/DatabaseReader+dump.swift"; sourceTree = "<group>"; };
		BF0FF276B04BEB26AFC029B8CC5737E2 /* QuoteDumpFormat.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = QuoteDumpFormat.swift; path = GRDB/Dump/DumpFormats/QuoteDumpFormat.swift; sourceTree = "<group>"; };
		BFA6F0F5FE050B562C777D626942CB7C /* FTS5.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = FTS5.swift; path = GRDB/FTS/FTS5.swift; sourceTree = "<group>"; };
		BFD070367F2F319AD6CF933D97801B70 /* TransactionObserver.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = TransactionObserver.swift; path = GRDB/Core/TransactionObserver.swift; sourceTree = "<group>"; };
		C07777F3CD33534A44167ED3F2C4EBB1 /* SQLTableGenerator.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = SQLTableGenerator.swift; path = GRDB/QueryInterface/SQLGeneration/SQLTableGenerator.swift; sourceTree = "<group>"; };
		C22EEE9B2C1992A8358C3EF7469E88AE /* TableRecord+Association.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "TableRecord+Association.swift"; path = "GRDB/QueryInterface/TableRecord+Association.swift"; sourceTree = "<group>"; };
		C2D2EF97F6E59A863A8E6204EED85ECB /* SQLSelection.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = SQLSelection.swift; path = GRDB/QueryInterface/SQL/SQLSelection.swift; sourceTree = "<group>"; };
		C3714481AE3A0ED65AF8FB726F4CB489 /* ValueConcurrentObserver.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ValueConcurrentObserver.swift; path = GRDB/ValueObservation/Observers/ValueConcurrentObserver.swift; sourceTree = "<group>"; };
		C448D5D4C453E874A234EBBD266877E8 /* NSData.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = NSData.swift; path = GRDB/Core/Support/Foundation/NSData.swift; sourceTree = "<group>"; };
		C4B8588DDC5999FC198F1CF4A2EC9B12 /* SQLJSONFunctions.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = SQLJSONFunctions.swift; path = GRDB/JSON/SQLJSONFunctions.swift; sourceTree = "<group>"; };
		C4BECD29EA2323E1F17BCD82D0A61333 /* DatabasePromise.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DatabasePromise.swift; path = GRDB/QueryInterface/SQL/DatabasePromise.swift; sourceTree = "<group>"; };
		C51ECB338F2B0DF1806C30357C0206B3 /* Export.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Export.swift; path = GRDB/Export.swift; sourceTree = "<group>"; };
		C5C0B086A2F93840BDCE1F8C7A5F1EE6 /* SQLOrdering.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = SQLOrdering.swift; path = GRDB/QueryInterface/SQL/SQLOrdering.swift; sourceTree = "<group>"; };
		C6CCFA10502F8EC4E4F2A30EC60A8269 /* MutablePersistableRecord+DAO.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "MutablePersistableRecord+DAO.swift"; path = "GRDB/Record/MutablePersistableRecord+DAO.swift"; sourceTree = "<group>"; };
		C7A9A6ED9B5A33578A78F217C2409143 /* FTS4.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = FTS4.swift; path = GRDB/FTS/FTS4.swift; sourceTree = "<group>"; };
		C9829C0EBD80520AAEE980B97DFE76B8 /* Pods-ModieshaAbstract-ModieshaNetworkExtension.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-ModieshaAbstract-ModieshaNetworkExtension.debug.xcconfig"; sourceTree = "<group>"; };
		C9EF6B1375FE4D341B96D6D98BD64D0C /* DatabasePool.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DatabasePool.swift; path = GRDB/Core/DatabasePool.swift; sourceTree = "<group>"; };
		CC245AE6D66EDBB5D52180C585361F9D /* MutablePersistableRecord+Delete.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "MutablePersistableRecord+Delete.swift"; path = "GRDB/Record/MutablePersistableRecord+Delete.swift"; sourceTree = "<group>"; };
		CCCE12721BC44C92694FC759D4967FAB /* SQLCollection.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = SQLCollection.swift; path = GRDB/QueryInterface/SQL/SQLCollection.swift; sourceTree = "<group>"; };
		CED33B3F922AF61214020F84684AF704 /* FetchableRecord+Decodable.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "FetchableRecord+Decodable.swift"; path = "GRDB/Record/FetchableRecord+Decodable.swift"; sourceTree = "<group>"; };
		D0AF446995D61FF0AD7F9DBF7CB1287F /* DatabaseWriter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DatabaseWriter.swift; path = GRDB/Core/DatabaseWriter.swift; sourceTree = "<group>"; };
		D0CF28EC5D51661EFE03EF4A32701EA8 /* DatabaseValue.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DatabaseValue.swift; path = GRDB/Core/DatabaseValue.swift; sourceTree = "<group>"; };
		D0DCB20DCC76EE2208220451DEAFF6AA /* OrderedDictionary.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = OrderedDictionary.swift; path = GRDB/Utils/OrderedDictionary.swift; sourceTree = "<group>"; };
		D1962053DFB78C1C8168956E4DE61E6B /* SQLInterpolation+QueryInterface.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "SQLInterpolation+QueryInterface.swift"; path = "GRDB/QueryInterface/SQLInterpolation+QueryInterface.swift"; sourceTree = "<group>"; };
		D1B4153E8A05D0614CD3D7C5D4E85622 /* URL.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = URL.swift; path = GRDB/Core/Support/Foundation/URL.swift; sourceTree = "<group>"; };
		D238CAE50DD6BA3D07C855A0243CDE74 /* RowAdapter.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = RowAdapter.swift; path = GRDB/Core/RowAdapter.swift; sourceTree = "<group>"; };
		D37EF498226DF3994651669AC231DCB3 /* StatementAuthorizer.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = StatementAuthorizer.swift; path = GRDB/Core/StatementAuthorizer.swift; sourceTree = "<group>"; };
		D511CCA94410FFC1737FB2B02887F4CB /* Row.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Row.swift; path = GRDB/Core/Row.swift; sourceTree = "<group>"; };
		D55B494AB8BA44925389910907C7E604 /* DumpFormat.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = DumpFormat.swift; path = GRDB/Dump/DumpFormat.swift; sourceTree = "<group>"; };
		D62EF6F49C256171325C7EE5EA33FAC6 /* Pods-ModieshaAbstract-ModieshaNetworkExtension.modulemap */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.module; path = "Pods-ModieshaAbstract-ModieshaNetworkExtension.modulemap"; sourceTree = "<group>"; };
		D6EE44820B2716F4D6B8C1EFC2CD43E7 /* GRDB.swift.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = GRDB.swift.release.xcconfig; sourceTree = "<group>"; };
		DB07150F7862C388A3D4C888A05BB390 /* ReadWriteBox.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ReadWriteBox.swift; path = GRDB/Utils/ReadWriteBox.swift; sourceTree = "<group>"; };
		******************************** /* NSNumber.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = NSNumber.swift; path = GRDB/Core/Support/Foundation/NSNumber.swift; sourceTree = "<group>"; };
		DB5D25AC4EB905EF523B754AB99FA3C5 /* FTS5+QueryInterface.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = "FTS5+QueryInterface.swift"; path = "GRDB/QueryInterface/FTS5+QueryInterface.swift"; sourceTree = "<group>"; };
		E2AC2D4493E3E72EE49ABA0598CBCBB6 /* Pool.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Pool.swift; path = GRDB/Utils/Pool.swift; sourceTree = "<group>"; };
		E63DB5BD9A31A74804661E5BF28B3665 /* FTS5Tokenizer.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = FTS5Tokenizer.swift; path = GRDB/FTS/FTS5Tokenizer.swift; sourceTree = "<group>"; };
		E68A13C0334AAAA9E7D0CBC6281FBA2D /* Pods-ModieshaAbstract-Modiesha-acknowledgements.plist */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.plist.xml; path = "Pods-ModieshaAbstract-Modiesha-acknowledgements.plist"; sourceTree = "<group>"; };
		E74FE2194DCCF8DDA1E34655224B8156 /* CaseInsensitiveIdentifier.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = CaseInsensitiveIdentifier.swift; path = GRDB/Utils/CaseInsensitiveIdentifier.swift; sourceTree = "<group>"; };
		E954404A7324673BD1A7C655BABF7027 /* Migration.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Migration.swift; path = GRDB/Migration/Migration.swift; sourceTree = "<group>"; };
		EDB4C17DEBFC19EDE67CFB85A3573A58 /* Statement.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Statement.swift; path = GRDB/Core/Statement.swift; sourceTree = "<group>"; };
		F501050AEF7F09600985F5F562508401 /* Pods-ModieshaAbstract-ModieshaNetworkExtension.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; path = "Pods-ModieshaAbstract-ModieshaNetworkExtension.release.xcconfig"; sourceTree = "<group>"; };
		F6A5EA9757AD0F4278108347CBCB68D9 /* Fetch.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Fetch.swift; path = GRDB/ValueObservation/Reducers/Fetch.swift; sourceTree = "<group>"; };
		F6A858103A81F9A3BD13BBF359AC501F /* Inflections.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Inflections.swift; path = GRDB/Utils/Inflections.swift; sourceTree = "<group>"; };
		F79E0BBB3949E7CE0D1BE00137D4CB59 /* ValueObservationScheduler.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ValueObservationScheduler.swift; path = GRDB/ValueObservation/ValueObservationScheduler.swift; sourceTree = "<group>"; };
		F81894840B7CB79E0671C18610C6FDD3 /* SQLRelation.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = SQLRelation.swift; path = GRDB/QueryInterface/SQL/SQLRelation.swift; sourceTree = "<group>"; };
		F89C5AC99BCEC3351694148501B34BA9 /* Date.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = Date.swift; path = GRDB/Core/Support/Foundation/Date.swift; sourceTree = "<group>"; };
		******************************** /* ReceiveValuesOn.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ReceiveValuesOn.swift; path = GRDB/Utils/ReceiveValuesOn.swift; sourceTree = "<group>"; };
		FB5584BFB9E4DBFBC980931D1A713791 /* GRDB.swift-dummy.m */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.c.objc; path = "GRDB.swift-dummy.m"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		0C72A20130AA88A6F1A2BAB831E79E56 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		4D6B4441E4B37D35D1993C6274D26966 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		EA57E1BA316AF4809E15F79C4F63499E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		2930F1C61A87DD35C7B042E163F84A6D /* Products */ = {
			isa = PBXGroup;
			children = (
				17ABD68E920293F080122810A8881638 /* GRDB.swift */,
				A147E080D60A6E236499B168CBF67FE4 /* Pods-ModieshaAbstract-Modiesha */,
				7A17806744DF62AE8CFA4B9C24679F45 /* Pods-ModieshaAbstract-ModieshaNetworkExtension */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		7A39077FA9AD4219EBB97C619AC8B6E0 /* Pods-ModieshaAbstract-ModieshaNetworkExtension */ = {
			isa = PBXGroup;
			children = (
				D62EF6F49C256171325C7EE5EA33FAC6 /* Pods-ModieshaAbstract-ModieshaNetworkExtension.modulemap */,
				51134571D7BE49B887CB0FACEB350D7F /* Pods-ModieshaAbstract-ModieshaNetworkExtension-acknowledgements.markdown */,
				494A688706416F6AF24F7BD863013859 /* Pods-ModieshaAbstract-ModieshaNetworkExtension-acknowledgements.plist */,
				93A6705E6BABC9E73591FBA1D15EA9D0 /* Pods-ModieshaAbstract-ModieshaNetworkExtension-dummy.m */,
				6A4EAFA201D4B7544FE65A0ABA5E7C9B /* Pods-ModieshaAbstract-ModieshaNetworkExtension-umbrella.h */,
				C9829C0EBD80520AAEE980B97DFE76B8 /* Pods-ModieshaAbstract-ModieshaNetworkExtension.debug.xcconfig */,
				F501050AEF7F09600985F5F562508401 /* Pods-ModieshaAbstract-ModieshaNetworkExtension.release.xcconfig */,
			);
			name = "Pods-ModieshaAbstract-ModieshaNetworkExtension";
			path = "Target Support Files/Pods-ModieshaAbstract-ModieshaNetworkExtension";
			sourceTree = "<group>";
		};
		8D15B1A1E02AC7E24445A42B5280DC4A /* standard */ = {
			isa = PBXGroup;
			children = (
				******************************** /* Association.swift */,
				B15331ECAA2DB0EE8B7F026273361A37 /* AssociationAggregate.swift */,
				AC6F69A431347A923AE32E711AF1036B /* BelongsToAssociation.swift */,
				E74FE2194DCCF8DDA1E34655224B8156 /* CaseInsensitiveIdentifier.swift */,
				B31256FFA058F94343D3C656F2FEF4FC /* CGFloat.swift */,
				51CCC9B7D4F7368C2FCBF41E18145CDD /* Column.swift */,
				9B7A1B09EF673371DB70CE9BD8F598D8 /* ColumnDefinition.swift */,
				37BF8404677F899C6C30BAA59253D42F /* CommonTableExpression.swift */,
				7D64A2B2338483128C97F43E6458CF6E /* Configuration.swift */,
				8218E7169BCBEDED85E4AAE2FEB29C93 /* Cursor.swift */,
				82B0B7B5D11A20DBE23140364BEC3DD4 /* Data.swift */,
				1583CC47BEDD1F7693E8531540F8B9AA /* Database.swift */,
				96DEFF6D3ECE2C89F1120C47CCC6BF0C /* Database+Dump.swift */,
				615EB3F81068E456BBD195FFEA5E4328 /* Database+Schema.swift */,
				07B47A9C3E533110DD7F5AE25761EB1A /* Database+SchemaDefinition.swift */,
				71F583D8FA7AB2D3A8E023F311093661 /* Database+Statements.swift */,
				554DA53556F07D4619BC8FC653FE1D94 /* DatabaseBackupProgress.swift */,
				2C99D87D1E10E59A4013A4EE24A752D9 /* DatabaseCancellable.swift */,
				897F3FC97B2081FC917411BF4590D32C /* DatabaseCollation.swift */,
				04B476B49DBFBC46E7EA7E75DBAF8EBA /* DatabaseDateComponents.swift */,
				51C4EB813981C04D0308AEE260A4327C /* DatabaseError.swift */,
				14E9A84063F394951713F7E568982ECA /* DatabaseFunction.swift */,
				69366054171AEE0EB896153D9AB0EB8A /* DatabaseMigrator.swift */,
				C9EF6B1375FE4D341B96D6D98BD64D0C /* DatabasePool.swift */,
				C4BECD29EA2323E1F17BCD82D0A61333 /* DatabasePromise.swift */,
				815023D50682D3F2E75DC9FC4188B71A /* DatabasePublishers.swift */,
				0620725A7D069E3ED5B5F55F5B944676 /* DatabaseQueue.swift */,
				A5ECFE7D3A8012D6AB3BE18B43EACEB2 /* DatabaseReader.swift */,
				BE1A1D95FE79F83C3F0063454AB40A30 /* DatabaseReader+dump.swift */,
				8A6B565106F35F08E99767A3BD0E665B /* DatabaseRegion.swift */,
				2D0614CE22C1D5EB335D8998F907F883 /* DatabaseRegionObservation.swift */,
				321E71AC72C7C068240A76EF7231C33C /* DatabaseSchemaCache.swift */,
				11852E03C78BAB0865A1D8726B5B4EFD /* DatabaseSnapshot.swift */,
				62D99F0E489DAFFB3BBC32D8714D2627 /* DatabaseSnapshotPool.swift */,
				D0CF28EC5D51661EFE03EF4A32701EA8 /* DatabaseValue.swift */,
				40B6FC2033DD5642D2B395CF9AC509ED /* DatabaseValueConvertible.swift */,
				7F05BE80FB8AD9271DA034909154A81B /* DatabaseValueConvertible+Decodable.swift */,
				4D4BB1F0155D4ABEFB2E557C5AC813E6 /* DatabaseValueConvertible+Encodable.swift */,
				9BC7D6668E0B972343963419EEB336C1 /* DatabaseValueConvertible+RawRepresentable.swift */,
				13A6F2F013641B994EF8AEADF76C734C /* DatabaseValueConvertible+ReferenceConvertible.swift */,
				D0AF446995D61FF0AD7F9DBF7CB1287F /* DatabaseWriter.swift */,
				F89C5AC99BCEC3351694148501B34BA9 /* Date.swift */,
				3ABA6321FEF4A2863DEB4671D2D082A4 /* DebugDumpFormat.swift */,
				BD4898E489E3D284A2BF14A7A0A4DF78 /* Decimal.swift */,
				D55B494AB8BA44925389910907C7E604 /* DumpFormat.swift */,
				021AA890A0DCF0117F99A82A7887B50D /* EncodableRecord.swift */,
				23DF3F34024629A7EFDCA05A285A53BF /* EncodableRecord+Encodable.swift */,
				C51ECB338F2B0DF1806C30357C0206B3 /* Export.swift */,
				F6A5EA9757AD0F4278108347CBCB68D9 /* Fetch.swift */,
				016E2061B097BFA66B10478D091044BA /* FetchableRecord.swift */,
				CED33B3F922AF61214020F84684AF704 /* FetchableRecord+Decodable.swift */,
				1A188B469CF426640CE36E6F056F4F0F /* FetchableRecord+TableRecord.swift */,
				4C6A01B557B8B476BFD61AF4AEF029E0 /* FetchRequest.swift */,
				8F786726DD4FF6006E09BF08E2E8A5D9 /* Fixits.swift */,
				A5B56609D6212E5B0A1DB61A189F2721 /* ForeignKey.swift */,
				6D4980C3E9F28FCB22C50249D9075068 /* ForeignKeyDefinition.swift */,
				4FBCE0192803776E24D2DD5C21E603B5 /* FTS3.swift */,
				1944D8DE5F4AB01AED9960066E2BB480 /* FTS3+QueryInterface.swift */,
				00B3CEDCD692D616D3C0FB40409CF433 /* FTS3Pattern.swift */,
				5ED9D6844541B43D775581DBB3B7CF43 /* FTS3TokenizerDescriptor.swift */,
				C7A9A6ED9B5A33578A78F217C2409143 /* FTS4.swift */,
				BFA6F0F5FE050B562C777D626942CB7C /* FTS5.swift */,
				DB5D25AC4EB905EF523B754AB99FA3C5 /* FTS5+QueryInterface.swift */,
				0DF5A81B026D5BE13D40FFF53710314B /* FTS5CustomTokenizer.swift */,
				22334CC0DC7B4F46313D8CDB3F7F28A0 /* FTS5Pattern.swift */,
				E63DB5BD9A31A74804661E5BF28B3665 /* FTS5Tokenizer.swift */,
				76B279BAEC64F976C831791D9F6084EF /* FTS5TokenizerDescriptor.swift */,
				388B2F1CD028B8F3574599CCD6A9FDD2 /* FTS5WrapperTokenizer.swift */,
				096B73D701075ED15872DDD32BB66D9C /* grdb_config.h */,
				9F1EBC3B29513F987DF553C66CCD7F26 /* HasManyAssociation.swift */,
				7AA6B68AD8F5A71CA5F93AE79B77A9C6 /* HasManyThroughAssociation.swift */,
				87EE6FB45D98A10D5F2C1B32DE597EA7 /* HasOneAssociation.swift */,
				950B9CA657F2617EDED101A99F3EE5C7 /* HasOneThroughAssociation.swift */,
				3F1BDADBB6D45319B3435D9C67A20AEC /* IndexDefinition.swift */,
				F6A858103A81F9A3BD13BBF359AC501F /* Inflections.swift */,
				1D0E0177B1367FD207DD0768F52E0BA8 /* Inflections+English.swift */,
				61263E84786D15358777F9F2CC0E7D03 /* JoinAssociation.swift */,
				63F04DD6EF6901CA0AE963B860AD4F14 /* JSONColumn.swift */,
				B5F25F0897539928F5D51BAACE5D2C13 /* JSONDumpFormat.swift */,
				ACBD16187D363B104ABB2B7C22322048 /* JSONRequiredEncoder.swift */,
				77019093D9B2F9A4E2A91AED17808715 /* LineDumpFormat.swift */,
				A50E9AEBBC53BE59D698CB96E9356768 /* ListDumpFormat.swift */,
				38DEDC908B46EDAB870C1F79C5DE3E73 /* LockedBox.swift */,
				******************************** /* Map.swift */,
				E954404A7324673BD1A7C655BABF7027 /* Migration.swift */,
				720A913243FE97B3F4397E93FF358607 /* MutablePersistableRecord.swift */,
				C6CCFA10502F8EC4E4F2A30EC60A8269 /* MutablePersistableRecord+DAO.swift */,
				CC245AE6D66EDBB5D52180C585361F9D /* MutablePersistableRecord+Delete.swift */,
				78C0D1A3B8E52B4A817276B7F7F78A7B /* MutablePersistableRecord+Insert.swift */,
				AE39C1D9B5E717C79FF3602886448E3F /* MutablePersistableRecord+Save.swift */,
				4551E2AED0594503189C472ABB31E179 /* MutablePersistableRecord+Update.swift */,
				14DF23AA4CE2CB94DCEE4BC4BFAD8FA8 /* MutablePersistableRecord+Upsert.swift */,
				C448D5D4C453E874A234EBBD266877E8 /* NSData.swift */,
				5311AD1A0301473E6E953DBB50802600 /* NSNull.swift */,
				******************************** /* NSNumber.swift */,
				8A77688DC09228793560E7C23A2D8F71 /* NSString.swift */,
				87FE65AA0EDEB1E83CB67C338E1B33D6 /* OnDemandFuture.swift */,
				37895F479324A9121D0B01B6741D3CF2 /* Optional.swift */,
				D0DCB20DCC76EE2208220451DEAFF6AA /* OrderedDictionary.swift */,
				8A91D0A9DDA70D06730E8AE4B2F0DB74 /* PersistableRecord.swift */,
				6F4883C0CAAB145A8CF3D790831C78E5 /* PersistableRecord+Insert.swift */,
				4178530D5612259DA2FA3F3858DD4F12 /* PersistableRecord+Save.swift */,
				AAEFDB9140E8D7FC5EA25E00A0EBB8B4 /* PersistableRecord+Upsert.swift */,
				E2AC2D4493E3E72EE49ABA0598CBCBB6 /* Pool.swift */,
				31A3311B39CA89447090A260F98A374B /* QueryInterfaceRequest.swift */,
				BF0FF276B04BEB26AFC029B8CC5737E2 /* QuoteDumpFormat.swift */,
				DB07150F7862C388A3D4C888A05BB390 /* ReadWriteBox.swift */,
				******************************** /* ReceiveValuesOn.swift */,
				7BF85F6F2ACEABEEFACBC4CA17B56085 /* Record.swift */,
				956960709BE3F74DFD182B9D341064B6 /* Refinable.swift */,
				05C0CF7729B3BD44508725A598A967DC /* RemoveDuplicates.swift */,
				8D0C41B09B5F6902CCD1B232932DD6B4 /* RequestProtocols.swift */,
				D511CCA94410FFC1737FB2B02887F4CB /* Row.swift */,
				D238CAE50DD6BA3D07C855A0243CDE74 /* RowAdapter.swift */,
				774DE9CC39DB0120A82960BADCCF2163 /* RowDecodingError.swift */,
				9FFD36A4AD5BD3EF8C77F20C09B4D253 /* SchedulingWatchdog.swift */,
				B7FEA070ACA579B706E22ADE2EB533EA /* SerializedDatabase.swift */,
				7FBAB9476E2C3CB744FC7DA2E200F4EB /* SharedValueObservation.swift */,
				10FC594F337E9FEB48BBBC8618B8D7DE /* SQL.swift */,
				A643D95E49C6F2F7A65F06B75CEF72EB /* SQLAssociation.swift */,
				CCCE12721BC44C92694FC759D4967FAB /* SQLCollection.swift */,
				8542EB95445579DC0C67C29B19FFCC7A /* SQLColumnGenerator.swift */,
				767700A99BF920BB767D62E48B7151AD /* SQLExpression.swift */,
				11889429415470253DB2BFC852D12026 /* SQLForeignKeyRequest.swift */,
				1009E22874D53C33546C454BBB3CD884 /* SQLFunctions.swift */,
				4A7F10CADD50929EF5A5362CB7CFD322 /* SQLGenerationContext.swift */,
				2AE829E4AD6B67D662C903E433969FFB /* SQLIndexGenerator.swift */,
				BC35C2AF4BB8A01B2ADCD4812F3115E4 /* SQLInterpolation.swift */,
				D1962053DFB78C1C8168956E4DE61E6B /* SQLInterpolation+QueryInterface.swift */,
				2304C1763E945E54C3C28E9485174327 /* SQLiteDateParser.swift */,
				6A262FB4924F5CD64B55DC036317EE28 /* SQLJSONExpressible.swift */,
				C4B8588DDC5999FC198F1CF4A2EC9B12 /* SQLJSONFunctions.swift */,
				ABFF93A854CFE71C275D09ED847B98DC /* SQLOperators.swift */,
				C5C0B086A2F93840BDCE1F8C7A5F1EE6 /* SQLOrdering.swift */,
				204AD90BB1D06BB88146588B15ACEE62 /* SQLQueryGenerator.swift */,
				F81894840B7CB79E0671C18610C6FDD3 /* SQLRelation.swift */,
				1736B1EAEB34F5BF2DD1EFE8947280BE /* SQLRequest.swift */,
				C2D2EF97F6E59A863A8E6204EED85ECB /* SQLSelection.swift */,
				44216A4D9279269162BD9014BE0AAED4 /* SQLSubquery.swift */,
				AB918EBFAC48537D25DE47CC7836F66B /* SQLTableAlterationGenerator.swift */,
				C07777F3CD33534A44167ED3F2C4EBB1 /* SQLTableGenerator.swift */,
				4A29E380EFA520EE44DAB0B63CE3BEFC /* StandardLibrary.swift */,
				EDB4C17DEBFC19EDE67CFB85A3573A58 /* Statement.swift */,
				D37EF498226DF3994651669AC231DCB3 /* StatementAuthorizer.swift */,
				B86DB54C8B71DBBF943081C7BC174D26 /* StatementColumnConvertible.swift */,
				6B9AD445394F39E5D2A69A543FF8BC8D /* Table.swift */,
				8A8B65ABA4C06E27438DB5ABBA6EAE48 /* TableAlteration.swift */,
				82A5AB72A281DBA3C388DD1C49C09AF7 /* TableDefinition.swift */,
				47EBFCC60F1F01C8D8599DEB8653799C /* TableRecord.swift */,
				C22EEE9B2C1992A8358C3EF7469E88AE /* TableRecord+Association.swift */,
				18128178E98F852DC1E50E60F456DC76 /* TableRecord+QueryInterfaceRequest.swift */,
				66F728148ADC5662C8ED900A2BA5BFB5 /* Trace.swift */,
				8B880C021E3EE9CE1607614CFDEC3554 /* TransactionClock.swift */,
				BFD070367F2F319AD6CF933D97801B70 /* TransactionObserver.swift */,
				D1B4153E8A05D0614CD3D7C5D4E85622 /* URL.swift */,
				428F746DB2BF088721787970C3205D87 /* Utils.swift */,
				AC809412C67409D65F131ACA3C1C9005 /* UUID.swift */,
				C3714481AE3A0ED65AF8FB726F4CB489 /* ValueConcurrentObserver.swift */,
				9C6963559C18640F4D700BF51B4C1378 /* ValueObservation.swift */,
				F79E0BBB3949E7CE0D1BE00137D4CB59 /* ValueObservationScheduler.swift */,
				6183D4280DF1812C2E42BC14C99E66B9 /* ValueReducer.swift */,
				68A89820B42F8589FAF2340F29D31BED /* ValueWriteOnlyObserver.swift */,
				2F5E2B9AA26CC514E436FEEDF709C9E7 /* VirtualTableModule.swift */,
				0C453FC093572099C191ED405BFF4138 /* WALSnapshot.swift */,
				198CD5B477C15883281A81E554D6F078 /* WALSnapshotTransaction.swift */,
			);
			name = standard;
			sourceTree = "<group>";
		};
		93F64E5C53486BAC7AAC26BFF310A522 /* GRDB.swift */ = {
			isa = PBXGroup;
			children = (
				8D15B1A1E02AC7E24445A42B5280DC4A /* standard */,
				E5C19C67BCD7617F4E213DCB9931DFE1 /* Support Files */,
			);
			name = GRDB.swift;
			path = GRDB.swift;
			sourceTree = "<group>";
		};
		AF3F14E28BA325D608986C76AA04904C /* Targets Support Files */ = {
			isa = PBXGroup;
			children = (
				DA64E0AC01BD9AFB6EE8444FE8C8559A /* Pods-ModieshaAbstract-Modiesha */,
				7A39077FA9AD4219EBB97C619AC8B6E0 /* Pods-ModieshaAbstract-ModieshaNetworkExtension */,
			);
			name = "Targets Support Files";
			sourceTree = "<group>";
		};
		B7C590C380D5FD85D98A6911C4AD19CF /* Pods */ = {
			isa = PBXGroup;
			children = (
				93F64E5C53486BAC7AAC26BFF310A522 /* GRDB.swift */,
			);
			name = Pods;
			sourceTree = "<group>";
		};
		CF1408CF629C7361332E53B88F7BD30C = {
			isa = PBXGroup;
			children = (
				9D940727FF8FB9C785EB98E56350EF41 /* Podfile */,
				D89477F20FB1DE18A04690586D7808C4 /* Frameworks */,
				B7C590C380D5FD85D98A6911C4AD19CF /* Pods */,
				2930F1C61A87DD35C7B042E163F84A6D /* Products */,
				AF3F14E28BA325D608986C76AA04904C /* Targets Support Files */,
			);
			sourceTree = "<group>";
		};
		D89477F20FB1DE18A04690586D7808C4 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		DA64E0AC01BD9AFB6EE8444FE8C8559A /* Pods-ModieshaAbstract-Modiesha */ = {
			isa = PBXGroup;
			children = (
				5DFB326A42D7B015A8C880684606F0B5 /* Pods-ModieshaAbstract-Modiesha.modulemap */,
				32F6C3E5C32949204F620A4469D31F2B /* Pods-ModieshaAbstract-Modiesha-acknowledgements.markdown */,
				E68A13C0334AAAA9E7D0CBC6281FBA2D /* Pods-ModieshaAbstract-Modiesha-acknowledgements.plist */,
				19A4A88B2BA859AD5F2EA19DA487AB1E /* Pods-ModieshaAbstract-Modiesha-dummy.m */,
				555916DFDA1773EBAD92C5E930EC715C /* Pods-ModieshaAbstract-Modiesha-umbrella.h */,
				27845748D29D2173E454A48D2FF7A17F /* Pods-ModieshaAbstract-Modiesha.debug.xcconfig */,
				B974416EDA6FEBE3C61AA4BC253913AA /* Pods-ModieshaAbstract-Modiesha.release.xcconfig */,
			);
			name = "Pods-ModieshaAbstract-Modiesha";
			path = "Target Support Files/Pods-ModieshaAbstract-Modiesha";
			sourceTree = "<group>";
		};
		E5C19C67BCD7617F4E213DCB9931DFE1 /* Support Files */ = {
			isa = PBXGroup;
			children = (
				777A27CF6277B7EFD3D287F10AD06B45 /* GRDB.swift.modulemap */,
				FB5584BFB9E4DBFBC980931D1A713791 /* GRDB.swift-dummy.m */,
				4B543A3EB29924C2EBC9F92CCE7D9151 /* GRDB.swift-prefix.pch */,
				B6CB1A3E4360B70F92D669B3856B0CC7 /* GRDB.swift-umbrella.h */,
				0B14E141F9E8D628BFE364458061CE6B /* GRDB.swift.debug.xcconfig */,
				D6EE44820B2716F4D6B8C1EFC2CD43E7 /* GRDB.swift.release.xcconfig */,
			);
			name = "Support Files";
			path = "../Target Support Files/GRDB.swift";
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		8426F96C3034266606A17026717F23D9 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				6080FC7FD24312978665FEBF2A635040 /* Pods-ModieshaAbstract-Modiesha-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B1CDD5E8427027DB29C9B8F5D55612D0 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				719D1A981BC527B38FB9E6B435A449FC /* GRDB.swift-umbrella.h in Headers */,
				A7F1BE15B9B04F454F86489D49E74AA8 /* grdb_config.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		D6F72976ADD7BD2A2D495671C2413DB0 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				AB5CB509F22C0ACB29612F1F34BE3F10 /* Pods-ModieshaAbstract-ModieshaNetworkExtension-umbrella.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		0A86C228CFCBC35A1FD284274ED0930F /* GRDB.swift */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 9900C008CEC13F0285E8062AA73D6EF0 /* Build configuration list for PBXNativeTarget "GRDB.swift" */;
			buildPhases = (
				B1CDD5E8427027DB29C9B8F5D55612D0 /* Headers */,
				48E8FDD03F817C3D2A6F981188345965 /* Sources */,
				4D6B4441E4B37D35D1993C6274D26966 /* Frameworks */,
				015485F5631BFABA7728B85E4C2C2562 /* Copy generated compatibility header */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = GRDB.swift;
			productName = GRDB.swift;
			productReference = 17ABD68E920293F080122810A8881638 /* GRDB.swift */;
			productType = "com.apple.product-type.library.static";
		};
		69840C0C0B5325C4AC65A9C90832860F /* Pods-ModieshaAbstract-Modiesha */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 9473B93FA95CDDFCB7BD3C741C094E42 /* Build configuration list for PBXNativeTarget "Pods-ModieshaAbstract-Modiesha" */;
			buildPhases = (
				8426F96C3034266606A17026717F23D9 /* Headers */,
				22696647CDAC5D2BEF492C6CE5055554 /* Sources */,
				EA57E1BA316AF4809E15F79C4F63499E /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				FF5AEB8BFF470EB56A572B0120A9C21B /* PBXTargetDependency */,
			);
			name = "Pods-ModieshaAbstract-Modiesha";
			productName = "Pods-ModieshaAbstract-Modiesha";
			productReference = A147E080D60A6E236499B168CBF67FE4 /* Pods-ModieshaAbstract-Modiesha */;
			productType = "com.apple.product-type.library.static";
		};
		F75D8710C21DF322E1FFF03DCB43042E /* Pods-ModieshaAbstract-ModieshaNetworkExtension */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = FFB388066A46128FB69B049AC104C637 /* Build configuration list for PBXNativeTarget "Pods-ModieshaAbstract-ModieshaNetworkExtension" */;
			buildPhases = (
				D6F72976ADD7BD2A2D495671C2413DB0 /* Headers */,
				33C16150E778A0043A73510B2E719801 /* Sources */,
				0C72A20130AA88A6F1A2BAB831E79E56 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				961B642E39CFB7C7A1F7D731E49A4E28 /* PBXTargetDependency */,
			);
			name = "Pods-ModieshaAbstract-ModieshaNetworkExtension";
			productName = "Pods-ModieshaAbstract-ModieshaNetworkExtension";
			productReference = 7A17806744DF62AE8CFA4B9C24679F45 /* Pods-ModieshaAbstract-ModieshaNetworkExtension */;
			productType = "com.apple.product-type.library.static";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		BFDFE7DC352907FC980B868725387E98 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 1600;
				LastUpgradeCheck = 1600;
			};
			buildConfigurationList = 4821239608C13582E20E6DA73FD5F1F9 /* Build configuration list for PBXProject "Pods" */;
			compatibilityVersion = "Xcode 16.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				Base,
				en,
			);
			mainGroup = CF1408CF629C7361332E53B88F7BD30C;
			minimizedProjectReferenceProxies = 0;
			preferredProjectObjectVersion = 77;
			productRefGroup = 2930F1C61A87DD35C7B042E163F84A6D /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				0A86C228CFCBC35A1FD284274ED0930F /* GRDB.swift */,
				69840C0C0B5325C4AC65A9C90832860F /* Pods-ModieshaAbstract-Modiesha */,
				F75D8710C21DF322E1FFF03DCB43042E /* Pods-ModieshaAbstract-ModieshaNetworkExtension */,
			);
		};
/* End PBXProject section */

/* Begin PBXShellScriptBuildPhase section */
		015485F5631BFABA7728B85E4C2C2562 /* Copy generated compatibility header */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${DERIVED_SOURCES_DIR}/${PRODUCT_MODULE_NAME}-Swift.h",
				"${PODS_ROOT}/Headers/Public/GRDB/GRDB.swift.modulemap",
				"${PODS_ROOT}/Headers/Public/GRDB/GRDB.swift-umbrella.h",
			);
			name = "Copy generated compatibility header";
			outputFileListPaths = (
			);
			outputPaths = (
				"${BUILT_PRODUCTS_DIR}/${PRODUCT_MODULE_NAME}.modulemap",
				"${BUILT_PRODUCTS_DIR}/GRDB.swift-umbrella.h",
				"${BUILT_PRODUCTS_DIR}/Swift Compatibility Header/${PRODUCT_MODULE_NAME}-Swift.h",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "COMPATIBILITY_HEADER_PATH=\"${BUILT_PRODUCTS_DIR}/Swift Compatibility Header/${PRODUCT_MODULE_NAME}-Swift.h\"\nMODULE_MAP_PATH=\"${BUILT_PRODUCTS_DIR}/${PRODUCT_MODULE_NAME}.modulemap\"\n\nditto \"${DERIVED_SOURCES_DIR}/${PRODUCT_MODULE_NAME}-Swift.h\" \"${COMPATIBILITY_HEADER_PATH}\"\nditto \"${PODS_ROOT}/Headers/Public/GRDB/GRDB.swift.modulemap\" \"${MODULE_MAP_PATH}\"\nditto \"${PODS_ROOT}/Headers/Public/GRDB/GRDB.swift-umbrella.h\" \"${BUILT_PRODUCTS_DIR}\"\nprintf \"\\n\\nmodule ${PRODUCT_MODULE_NAME}.Swift {\\n  header \\\"${COMPATIBILITY_HEADER_PATH}\\\"\\n  requires objc\\n}\\n\" >> \"${MODULE_MAP_PATH}\"\n";
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		22696647CDAC5D2BEF492C6CE5055554 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F984A7A0AE4D5CE9B3E96ED9A752ED77 /* Pods-ModieshaAbstract-Modiesha-dummy.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		33C16150E778A0043A73510B2E719801 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				29679F65DC29FA03F351AA1B7B45ACA9 /* Pods-ModieshaAbstract-ModieshaNetworkExtension-dummy.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		48E8FDD03F817C3D2A6F981188345965 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				FBCAEAFBF3A5462C5B6E93247C4D1E9A /* Association.swift in Sources */,
				7E9DF8DCDE261ABE002A117CD28F600C /* AssociationAggregate.swift in Sources */,
				44A4365B0A0EED72AB71CC0E59C36883 /* BelongsToAssociation.swift in Sources */,
				A1F29F0E14493C7DCE19B0156ABAEAD7 /* CaseInsensitiveIdentifier.swift in Sources */,
				9EFD54177423F253F8ABEF517D19144D /* CGFloat.swift in Sources */,
				DB868D713731EED3B360B53D1E823F7B /* Column.swift in Sources */,
				0CC206146A5DBB56C7E9C3AD7EDA3781 /* ColumnDefinition.swift in Sources */,
				4C6391CDB5EACF86798402C835AF56DD /* CommonTableExpression.swift in Sources */,
				4889CF223D36C71EB04F36DE6062508F /* Configuration.swift in Sources */,
				F0A27163430D8BC84BBA3379C05F332A /* Cursor.swift in Sources */,
				******************************** /* Data.swift in Sources */,
				37BD4A55FC28E6B1A610FD3A2376B15C /* Database.swift in Sources */,
				01C725BA646437F24BF280B73E8B3DC0 /* Database+Dump.swift in Sources */,
				A0F4B98FC16B042EF7EFDC6B1339CAB5 /* Database+Schema.swift in Sources */,
				068CFEB7D45924AC6A10ED742F0FDFA2 /* Database+SchemaDefinition.swift in Sources */,
				C49614D99B691A36496738B94914654B /* Database+Statements.swift in Sources */,
				F3BCE49BBFCA025AB8D2CE3225B2D024 /* DatabaseBackupProgress.swift in Sources */,
				2A4FFB04A0EA46438DC6CB23F047743F /* DatabaseCancellable.swift in Sources */,
				04C16EE845D2CEAF7A0E433E67B93028 /* DatabaseCollation.swift in Sources */,
				D9BA53490093CD9F1833CE3F0C972E4C /* DatabaseDateComponents.swift in Sources */,
				579BBD9CEE26494007F147B14A6A24F1 /* DatabaseError.swift in Sources */,
				A8E8CE0CA7FD9914124D0056EE9926E1 /* DatabaseFunction.swift in Sources */,
				C08F87E122D7F06C907AA749B2D68515 /* DatabaseMigrator.swift in Sources */,
				39DF54D359A58F3FDAFA973EC432C401 /* DatabasePool.swift in Sources */,
				8A244918EF1DE6AEE6B9C76C1761F447 /* DatabasePromise.swift in Sources */,
				CD46BB1B71339D4CF47B6A4B1DCDA6DD /* DatabasePublishers.swift in Sources */,
				A2170156C14C516584217F1843C0FE3D /* DatabaseQueue.swift in Sources */,
				AB51BF708E54DB0B8B69B230F0FD14E5 /* DatabaseReader.swift in Sources */,
				C8FD6BDAFF4D62033673FA17AF5664C6 /* DatabaseReader+dump.swift in Sources */,
				F14F594508B24B722CEAD4706842D769 /* DatabaseRegion.swift in Sources */,
				4CE7F6A649E847355C154CD34A14B33C /* DatabaseRegionObservation.swift in Sources */,
				122770537F9A40A084D46CB3C3DEA46B /* DatabaseSchemaCache.swift in Sources */,
				A50876A69CA5FD5C55F31D282F75224F /* DatabaseSnapshot.swift in Sources */,
				6206EACBC2A1C270C714C982624EC60E /* DatabaseSnapshotPool.swift in Sources */,
				CCB6D465BA977EFDA2EA3FBA920B8CBE /* DatabaseValue.swift in Sources */,
				46DCB5B1AB91AF27ABF6B9633BF9CF20 /* DatabaseValueConvertible.swift in Sources */,
				91CC45A182AB6CBE4A331B98B07232C5 /* DatabaseValueConvertible+Decodable.swift in Sources */,
				AD03BC4FFEAEB317A322440981DD7E79 /* DatabaseValueConvertible+Encodable.swift in Sources */,
				F1A40189ED776C0C17311545E8283D9B /* DatabaseValueConvertible+RawRepresentable.swift in Sources */,
				E5DD4F4F54F2FE80D09A870945C8FF05 /* DatabaseValueConvertible+ReferenceConvertible.swift in Sources */,
				D6267A09CFBC101D3D5071DB46665AAB /* DatabaseWriter.swift in Sources */,
				B32B045F01565994FAB625B0BB73ACEB /* Date.swift in Sources */,
				A37C69856BE94E58115989CDB1EB01F3 /* DebugDumpFormat.swift in Sources */,
				CE8CDF07F22ED0C8C0A70DE4FC119214 /* Decimal.swift in Sources */,
				B2BE472C1B222D1613D145588223CEFE /* DumpFormat.swift in Sources */,
				74EE559C20ABCCE9B78BB8139587D5DA /* EncodableRecord.swift in Sources */,
				76CD7FBF2D100DD2B8D4DE7AED64BDD8 /* EncodableRecord+Encodable.swift in Sources */,
				7D267174D805DB21AD3557F9D39692AD /* Export.swift in Sources */,
				3A36772FF42CA5B29FBA03D24703A7BF /* Fetch.swift in Sources */,
				9BF46DB3441DA01E04011889B61D1EEA /* FetchableRecord.swift in Sources */,
				F7D480C5BE155827A6D088F17C50173F /* FetchableRecord+Decodable.swift in Sources */,
				D22E377D0285C71432467B5D320C4AA0 /* FetchableRecord+TableRecord.swift in Sources */,
				B0F6DA5F8CC7A0D2B0BBC1FF8A3B9FD0 /* FetchRequest.swift in Sources */,
				BB6769D12569C9AAC0F9F0994111972E /* Fixits.swift in Sources */,
				DB9BC9316B737AE30C54408BF2912E86 /* ForeignKey.swift in Sources */,
				56E6660506B4A337650D2DFCB70BF834 /* ForeignKeyDefinition.swift in Sources */,
				B1DBF7F0372A52425FE65305D9E93900 /* FTS3.swift in Sources */,
				******************************** /* FTS3+QueryInterface.swift in Sources */,
				B900B923D0744A58D3784DA2C75DE1A1 /* FTS3Pattern.swift in Sources */,
				C957A922B05F6471CAB45281A4F771F3 /* FTS3TokenizerDescriptor.swift in Sources */,
				60F4AFFD4FF5EBD37D18A586CF6691E3 /* FTS4.swift in Sources */,
				A5B63E3A7CCC216680DEE3AAB3A33136 /* FTS5.swift in Sources */,
				6DCF31F8D11CB6EC6C63DF4BE8D787B7 /* FTS5+QueryInterface.swift in Sources */,
				2C0E271BBFB9E1A29D9895248FF44A03 /* FTS5CustomTokenizer.swift in Sources */,
				1D5638DA09DFE646F23AE3B1A6B25D15 /* FTS5Pattern.swift in Sources */,
				4B4A72804C3F6C00B31F3106653B0DF7 /* FTS5Tokenizer.swift in Sources */,
				6861A1E2F13EB483894E95A46FBDDE42 /* FTS5TokenizerDescriptor.swift in Sources */,
				8E3967745DC97334097947C7874F990F /* FTS5WrapperTokenizer.swift in Sources */,
				25EA7D0A2FD4DA2087F9F41924CD8E87 /* GRDB.swift-dummy.m in Sources */,
				0B395D2CA34FF29003259CD0B621695C /* HasManyAssociation.swift in Sources */,
				C5668DE51090BD6A95197268E5817567 /* HasManyThroughAssociation.swift in Sources */,
				1B0A360705A76416DCF4CF58EE8B4ABC /* HasOneAssociation.swift in Sources */,
				1E76A5A8A6A182CA47A33F8580E2E1D5 /* HasOneThroughAssociation.swift in Sources */,
				E97202A7B5B9D4C1F5C696C829D91161 /* IndexDefinition.swift in Sources */,
				0FB973490139F3E6A9DF913D3D6F4EAF /* Inflections.swift in Sources */,
				05EE813C6C3CB374EE41B8DD3C84D5B3 /* Inflections+English.swift in Sources */,
				0A5E233E72F68CF7489EB9DBE7CDF4F6 /* JoinAssociation.swift in Sources */,
				C6A0FAA90F8544F3A2280AFEA38696D4 /* JSONColumn.swift in Sources */,
				BAB044DA7C5C1BF240E47D648D6DE3FD /* JSONDumpFormat.swift in Sources */,
				922C8E6264A3030D185C3748E5AC5D3C /* JSONRequiredEncoder.swift in Sources */,
				DC73C142BAFFA8B25315E39C6266F271 /* LineDumpFormat.swift in Sources */,
				4FBCAA689F9C5B3D0349456C242B45E7 /* ListDumpFormat.swift in Sources */,
				7379860E031C6AED9376E5AB3EA3E1B6 /* LockedBox.swift in Sources */,
				******************************** /* Map.swift in Sources */,
				E15158C49D0DD7EB3371F7CC91C10236 /* Migration.swift in Sources */,
				F5615C419817350F24FAF9F6B6438156 /* MutablePersistableRecord.swift in Sources */,
				9088193B7F69CB2431E331F96C433CD9 /* MutablePersistableRecord+DAO.swift in Sources */,
				F3406678A1B9EAFCAAB07F4D72446BA0 /* MutablePersistableRecord+Delete.swift in Sources */,
				9E9540690763E70F69FF8903573097CA /* MutablePersistableRecord+Insert.swift in Sources */,
				60C37199111DEFA343B4F030FDEB6A38 /* MutablePersistableRecord+Save.swift in Sources */,
				E3DC8078E3B7426333D874068DFC1575 /* MutablePersistableRecord+Update.swift in Sources */,
				87F0746BFDCD45B8C46E02E2D74A37A7 /* MutablePersistableRecord+Upsert.swift in Sources */,
				280871BA26C76A6B5CA7DF3DD016E2FE /* NSData.swift in Sources */,
				40B473B444920D42A390924414A0615A /* NSNull.swift in Sources */,
				97BB035F44E8EE796D6BE06C42DA20F6 /* NSNumber.swift in Sources */,
				FF1BDBD29C9ED3EC0ED58A71A98430F1 /* NSString.swift in Sources */,
				108502126D9CED9969A936103A832B35 /* OnDemandFuture.swift in Sources */,
				C5E69855EF6B369C8C28E4C689CE803D /* Optional.swift in Sources */,
				9F502391B6D0A632CC8BED7ACD19E180 /* OrderedDictionary.swift in Sources */,
				3AEED39A57CDFAB925B46667E46BA7EC /* PersistableRecord.swift in Sources */,
				47802F0AE1E52F0139B8875C935C5E0A /* PersistableRecord+Insert.swift in Sources */,
				0BA95943BCAF0506500A17E7AAADF9F5 /* PersistableRecord+Save.swift in Sources */,
				207361AB8E442E2B334189A40D3F83F2 /* PersistableRecord+Upsert.swift in Sources */,
				C701C33C6B39C2B4D689638C526F3A2E /* Pool.swift in Sources */,
				511CB376AF3CF90247A470A47C359579 /* QueryInterfaceRequest.swift in Sources */,
				40F7443B414418C95439F04438687FA0 /* QuoteDumpFormat.swift in Sources */,
				AE6DF63A531C4419EB2126856BF4EFFA /* ReadWriteBox.swift in Sources */,
				******************************** /* ReceiveValuesOn.swift in Sources */,
				68D9B3C0458FF0101EAE6ADEA4D46662 /* Record.swift in Sources */,
				F0F949EA9B0BC2294C1B11AF82EEF54D /* Refinable.swift in Sources */,
				568336004A1E7D9D8E3F04BCC0368483 /* RemoveDuplicates.swift in Sources */,
				DE01D3E37CE04761FC7DE1308C39FF29 /* RequestProtocols.swift in Sources */,
				9B949BA71E23B8AFA2A3486D64039618 /* Row.swift in Sources */,
				1F5D4AE21E45A22579F6F1604CF4C3A2 /* RowAdapter.swift in Sources */,
				CAEB662257512CC8051BCB4535CEC8DC /* RowDecodingError.swift in Sources */,
				8F6C239985DACFEA248EA198892B05AE /* SchedulingWatchdog.swift in Sources */,
				B4AB7A8F20500435056CA49B488FADD6 /* SerializedDatabase.swift in Sources */,
				521B6E6177E3B70DBEB68D67D78B58AF /* SharedValueObservation.swift in Sources */,
				138B831FB587C94E8288BAA6871BAA56 /* SQL.swift in Sources */,
				A2BFB3D0D509871593316D00E537D342 /* SQLAssociation.swift in Sources */,
				3398C643E437D24503E9EC98AB56EB38 /* SQLCollection.swift in Sources */,
				2359F870120FA49BFAAF48CE41B70DE7 /* SQLColumnGenerator.swift in Sources */,
				A94367AE6C7B668A10C87E3A5C6603CC /* SQLExpression.swift in Sources */,
				BD18C0573E08E520AA3BC20A42725ED4 /* SQLForeignKeyRequest.swift in Sources */,
				7641648EB92CBF0569E29DD752CDFCB5 /* SQLFunctions.swift in Sources */,
				97BB078BD5F9041D975425F678BE726E /* SQLGenerationContext.swift in Sources */,
				D89261C389582DF3658F98B4341D2068 /* SQLIndexGenerator.swift in Sources */,
				8FF2E305CDE8EA4CFE832BFD6860428C /* SQLInterpolation.swift in Sources */,
				E6D79E22993B1B75F3DF19CDAA2161C6 /* SQLInterpolation+QueryInterface.swift in Sources */,
				3FF3B3A8EAF5346BB9FC6EA7B97B9ACF /* SQLiteDateParser.swift in Sources */,
				07C70C592165C5759D92C570595A6308 /* SQLJSONExpressible.swift in Sources */,
				6E5872F3A199DA240E8627F270251CA5 /* SQLJSONFunctions.swift in Sources */,
				1EFCBAC2136290B35A7364342D3DED6E /* SQLOperators.swift in Sources */,
				5D6C0D8DB6B4B36CB6672435837DDF9A /* SQLOrdering.swift in Sources */,
				CB9F228B087D60BBAC0572AB2E0899FE /* SQLQueryGenerator.swift in Sources */,
				FB62B1ACDD2DDEB9BE1E1353FA44DB2F /* SQLRelation.swift in Sources */,
				9BD3CC38755E0B8AE5D9D8959FEB23B7 /* SQLRequest.swift in Sources */,
				E567B2CB5242CD050705D9BB678C3F4A /* SQLSelection.swift in Sources */,
				1C0B9ED8F8D46398AB81A246B7C2CC91 /* SQLSubquery.swift in Sources */,
				47F75CC7252EBFA9747AAD6F729A7E4F /* SQLTableAlterationGenerator.swift in Sources */,
				274C2FCA2808D52117D7C2DE355D4B0A /* SQLTableGenerator.swift in Sources */,
				EC131593FA475D137D0EEDEAD55C0C7B /* StandardLibrary.swift in Sources */,
				CCF03CD2F6FAB48507716F17D1C2251D /* Statement.swift in Sources */,
				EB0E6259B434D8A6E2070D8A7356360B /* StatementAuthorizer.swift in Sources */,
				046AECD21FBF3338A490615B157AADD1 /* StatementColumnConvertible.swift in Sources */,
				745ED5BBBA769D79C2175E1EC3F20D24 /* Table.swift in Sources */,
				FB7E24EDF342A87947A9384E030C9D91 /* TableAlteration.swift in Sources */,
				BE5E634346551CD93D6625C980A310A4 /* TableDefinition.swift in Sources */,
				3DCDCAB63AD77FCC8678F7A5B45CE8C5 /* TableRecord.swift in Sources */,
				6C590CAD0BBBD4E2376CFFC47D243206 /* TableRecord+Association.swift in Sources */,
				D967C68ABAD9608D9795FCED447FB4BF /* TableRecord+QueryInterfaceRequest.swift in Sources */,
				0A31F5EA78AFA1273696C973431369EC /* Trace.swift in Sources */,
				B6FEA11600C65EB7B31E8B3EF863434E /* TransactionClock.swift in Sources */,
				4D5F42A43B21113BC99D47AADACA064D /* TransactionObserver.swift in Sources */,
				511BA44015032AFAF62CF7D295D5A35C /* URL.swift in Sources */,
				A7F8688215DAC06C49979DB5F8EE2628 /* Utils.swift in Sources */,
				0BE3BEEE6DD25252470315E77193947B /* UUID.swift in Sources */,
				DA1A0291468B6C4774DAC5CA51DA26B9 /* ValueConcurrentObserver.swift in Sources */,
				A4525BB70FF4D60E96CDBC4788C4833C /* ValueObservation.swift in Sources */,
				7914EEF51E70148EA48B8BB12964325D /* ValueObservationScheduler.swift in Sources */,
				9B9B03495E56A652F94D614B1363391E /* ValueReducer.swift in Sources */,
				6CD16FAC56454DC593E60598C3CEC5C2 /* ValueWriteOnlyObserver.swift in Sources */,
				46DCFFFF7C352F9E8DE1F03E2DC387A6 /* VirtualTableModule.swift in Sources */,
				8AF221F84AE31B18AD7D65566618AAD2 /* WALSnapshot.swift in Sources */,
				EC9810D6FA92C63FD0ECAA1CD6798E87 /* WALSnapshotTransaction.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		961B642E39CFB7C7A1F7D731E49A4E28 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = GRDB.swift;
			target = 0A86C228CFCBC35A1FD284274ED0930F /* GRDB.swift */;
			targetProxy = 86CD9CD4EFD21F56EF67E5D2FC51C274 /* PBXContainerItemProxy */;
		};
		FF5AEB8BFF470EB56A572B0120A9C21B /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = GRDB.swift;
			target = 0A86C228CFCBC35A1FD284274ED0930F /* GRDB.swift */;
			targetProxy = 179987DA720CD28449502CE854BD5062 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		14A483EA9A0A5F2FFCAEE2B5AC8359AF /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = D6EE44820B2716F4D6B8C1EFC2CD43E7 /* GRDB.swift.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/GRDB.swift/GRDB.swift-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				MODULEMAP_FILE = Headers/Public/GRDB/GRDB.swift.modulemap;
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PRIVATE_HEADERS_FOLDER_PATH = "";
				PRODUCT_MODULE_NAME = GRDB;
				PRODUCT_NAME = GRDB.swift;
				PUBLIC_HEADERS_FOLDER_PATH = "";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.7;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		1FF893407926DC6D0B78D74BCB201368 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = C9829C0EBD80520AAEE980B97DFE76B8 /* Pods-ModieshaAbstract-ModieshaNetworkExtension.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-ModieshaAbstract-ModieshaNetworkExtension/Pods-ModieshaAbstract-ModieshaNetworkExtension.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		30E0B9EFD9A5C45D0D351231E81B30B3 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"POD_CONFIGURATION_RELEASE=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_INSTALLED_PRODUCT = NO;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
				SYMROOT = "${SRCROOT}/../build";
			};
			name = Release;
		};
		C69CD2B16D5594219C44DD1F92526C3F /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = F501050AEF7F09600985F5F562508401 /* Pods-ModieshaAbstract-ModieshaNetworkExtension.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-ModieshaAbstract-ModieshaNetworkExtension/Pods-ModieshaAbstract-ModieshaNetworkExtension.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		C8C8B9DB9E7893B44EFB80C5A6B7DACB /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = B974416EDA6FEBE3C61AA4BC253913AA /* Pods-ModieshaAbstract-Modiesha.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-ModieshaAbstract-Modiesha/Pods-ModieshaAbstract-Modiesha.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		F4FF6A0D1970CA9705974E3CB2134802 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"POD_CONFIGURATION_DEBUG=1",
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				STRIP_INSTALLED_PRODUCT = NO;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				SYMROOT = "${SRCROOT}/../build";
			};
			name = Debug;
		};
		FCF7AC7B86E9DEE8795A2941186E48FE /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 0B14E141F9E8D628BFE364458061CE6B /* GRDB.swift.debug.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREFIX_HEADER = "Target Support Files/GRDB.swift/GRDB.swift-prefix.pch";
				GENERATE_INFOPLIST_FILE = NO;
				IPHONEOS_DEPLOYMENT_TARGET = 11.0;
				MODULEMAP_FILE = Headers/Public/GRDB/GRDB.swift.modulemap;
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PRIVATE_HEADERS_FOLDER_PATH = "";
				PRODUCT_MODULE_NAME = GRDB;
				PRODUCT_NAME = GRDB.swift;
				PUBLIC_HEADERS_FOLDER_PATH = "";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) ";
				SWIFT_INSTALL_OBJC_HEADER = YES;
				SWIFT_VERSION = 5.7;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		FE234A5C7952AB43627E8FE592E84D03 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 27845748D29D2173E454A48D2FF7A17F /* Pods-ModieshaAbstract-Modiesha.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = NO;
				"CODE_SIGN_IDENTITY[sdk=appletvos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				"CODE_SIGN_IDENTITY[sdk=watchos*]" = "";
				ENABLE_MODULE_VERIFIER = NO;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				IPHONEOS_DEPLOYMENT_TARGET = 16.0;
				MACH_O_TYPE = staticlib;
				MODULEMAP_FILE = "Target Support Files/Pods-ModieshaAbstract-Modiesha/Pods-ModieshaAbstract-Modiesha.modulemap";
				OTHER_LDFLAGS = "";
				OTHER_LIBTOOLFLAGS = "";
				PODS_ROOT = "$(SRCROOT)";
				PRODUCT_BUNDLE_IDENTIFIER = "org.cocoapods.${PRODUCT_NAME:rfc1034identifier}";
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		4821239608C13582E20E6DA73FD5F1F9 /* Build configuration list for PBXProject "Pods" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F4FF6A0D1970CA9705974E3CB2134802 /* Debug */,
				30E0B9EFD9A5C45D0D351231E81B30B3 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		9473B93FA95CDDFCB7BD3C741C094E42 /* Build configuration list for PBXNativeTarget "Pods-ModieshaAbstract-Modiesha" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				FE234A5C7952AB43627E8FE592E84D03 /* Debug */,
				C8C8B9DB9E7893B44EFB80C5A6B7DACB /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		9900C008CEC13F0285E8062AA73D6EF0 /* Build configuration list for PBXNativeTarget "GRDB.swift" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				FCF7AC7B86E9DEE8795A2941186E48FE /* Debug */,
				14A483EA9A0A5F2FFCAEE2B5AC8359AF /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		FFB388066A46128FB69B049AC104C637 /* Build configuration list for PBXNativeTarget "Pods-ModieshaAbstract-ModieshaNetworkExtension" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1FF893407926DC6D0B78D74BCB201368 /* Debug */,
				C69CD2B16D5594219C44DD1F92526C3F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = BFDFE7DC352907FC980B868725387E98 /* Project object */;
}
