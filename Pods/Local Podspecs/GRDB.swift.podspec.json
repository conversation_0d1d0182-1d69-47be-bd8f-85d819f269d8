{"name": "GRDB.swift", "version": "6.29.0", "license": {"type": "MIT", "file": "LICENSE"}, "summary": "A toolkit for SQLite databases, with a focus on application development.", "homepage": "https://github.com/groue/GRDB.swift", "authors": {"Gwendal Roué": "<EMAIL>"}, "source": {"git": "https://github.com/groue/GRDB.swift.git", "tag": "v6.29.0"}, "module_name": "GRDB", "swift_versions": ["5.7"], "platforms": {"ios": "11.0", "osx": "10.13", "watchos": "4.0", "tvos": "11.0"}, "default_subspecs": "standard", "subspecs": [{"name": "standard", "source_files": ["GRDB/**/*.swift", "Support/grdb_config.h"], "frameworks": "Foundation", "libraries": "sqlite3", "xcconfig": {"OTHER_SWIFT_FLAGS": "$(inherited) -D SQLITE_ENABLE_FTS5"}}, {"name": "SQLCipher", "source_files": ["GRDB/**/*.swift", "Support/SQLCipher_config.h"], "frameworks": "Foundation", "dependencies": {"SQLCipher": [">= 3.4.2"]}, "xcconfig": {"OTHER_SWIFT_FLAGS": "$(inherited) -D SQLITE_HAS_CODEC -D GRDBCIPHER -D SQLITE_ENABLE_FTS5", "OTHER_CFLAGS": "$(inherited) -DSQLIT<PERSON>_HAS_CODEC -DGRD<PERSON>IPHER -DSQLITE_ENABLE_FTS5", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) SQLITE_HAS_CODEC=1 GRDBCIPHER=1 SQLITE_ENABLE_FTS5=1"}}], "swift_version": "5.7"}