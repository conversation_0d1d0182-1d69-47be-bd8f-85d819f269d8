# Add GRDB and BinaryCodable Packages Guide

## Overview
This guide will help you manually add GRDB and BinaryCodable packages to your Xcode project to resolve the build errors.

## Step 1: Open Xcode Project
1. **Open `Modiesha.xcodeproj` in Xcode**
2. **Wait for Xcode to fully load the project**

## Step 2: Remove Existing Package References (If Any)
1. **Select the Modiesha project** (top of the navigator)
2. **Go to "Package Dependencies" tab**
3. **If you see any existing packages, remove them:**
   - Select each package and click the "-" button
   - This clears any corrupted references

## Step 3: Add BinaryCodable Package
1. **Go to File → Add Package Dependencies...**
2. **Enter the URL:** `https://github.com/christophhagen/BinaryCodable`
3. **Set Dependency Rule:** "Up to Next Major Version" starting from `3.0.0`
4. **Click "Add Package"**
5. **Select targets to add to:**
   - ✅ **Modiesha** (main app)
   - ✅ **ModieshaNetworkExtension**
   - ❌ ModieshaTests (optional)
   - ❌ ModieshaUITests (optional)
6. **Click "Add Package"**

## Step 4: Add GRDB Package
1. **Go to File → Add Package Dependencies...**
2. **Enter the URL:** `https://github.com/groue/GRDB.swift.git`
3. **Set Dependency Rule:** "Up to Next Major Version" starting from `6.0.0`
   - ⚠️ **Important:** Don't use "Branch: master" - use a stable version
4. **Click "Add Package"**
5. **Select products to add:**
   - ✅ **GRDB** (for main app)
   - ❌ GRDB-dynamic (not needed)
6. **Select targets:**
   - ✅ **Modiesha** (main app)
   - ✅ **ModieshaNetworkExtension** (if database is needed in extension)
7. **Click "Add Package"**

## Step 5: Verify Package Addition
1. **Check Package Dependencies tab:**
   - Should show BinaryCodable @ 3.x.x
   - Should show GRDB.swift @ 6.x.x
2. **Check target dependencies:**
   - Select Modiesha target → General tab → Frameworks, Libraries, and Embedded Content
   - Should see BinaryCodable and GRDB listed

## Step 6: Clean and Build
1. **Clean the project:** `Product → Clean Build Folder` (Cmd+Shift+K)
2. **Build the project:** `Product → Build` (Cmd+B)

## Expected Results
After adding packages correctly:
- ✅ No "No such module 'GRDB'" errors
- ✅ No "No such module 'BinaryCodable'" errors
- ✅ Package dependency graph resolves correctly
- ✅ Project builds successfully

## Troubleshooting

### If Package Addition Fails:
1. **Quit Xcode completely**
2. **Delete derived data:** `~/Library/Developer/Xcode/DerivedData/Modiesha-*`
3. **Restart Xcode and try again**

### If Build Still Fails:
1. **Check internet connection** (packages need to be downloaded)
2. **Try adding packages one at a time** (BinaryCodable first, then GRDB)
3. **Use stable versions, not master branch**

### Alternative Package URLs:
- **BinaryCodable:** `https://github.com/christophhagen/BinaryCodable.git`
- **GRDB:** `https://github.com/groue/GRDB.swift`

## Package Versions to Use
- **BinaryCodable:** 3.0.3 or later (stable)
- **GRDB:** 6.0.0 or later (stable, avoid master branch)

## Files That Need These Packages
### GRDB is used in:
- `Library/Database/Profile.swift`
- `Library/Database/ProfileManager.swift`
- `Library/Database/Databse.swift`
- `Library/Database/Profile+Update.swift`
- `Library/Database/ShadredPreferences+Database.swift`

### BinaryCodable is used in:
- Network Extension components
- Configuration parsing

## After Successful Addition
Once packages are added successfully:
1. **Test the build** - should complete without package errors
2. **Test VPN functionality** - core features should work
3. **Test database features** - profile management should work

## Next Steps
After packages are working:
1. **Focus on VPN testing** - configuration selection and connection
2. **Add Libbox.xcframework to Network Extension** - for real proxying
3. **Test end-to-end flow** - import config → select → connect

The VPN functionality we've been working on should be fully functional once the package dependencies are resolved!
