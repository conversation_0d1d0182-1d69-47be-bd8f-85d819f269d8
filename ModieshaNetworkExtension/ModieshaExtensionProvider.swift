import Foundation
import NetworkExtension

/// ModieshaExtensionProvider - A bridge implementation that follows SagerNet pattern
/// This provides the proper sing-box integration while working with existing Modiesha architecture
class ModieshaExtensionProvider: NEPacketTunnelProvider {
    
    // MARK: - Properties
    private var boxService: Any? // Will be LibboxBoxService when framework is linked
    private var commandServer: Any? // Will be LibboxCommandServer when framework is linked
    private var platformInterface: ModieshaPlatformInterface?
    private var isServiceRunning = false
    private var configurationContent: String?
    
    // MARK: - NEPacketTunnelProvider Overrides
    
    override func startTunnel(options: [String: NSObject]?, completionHandler: @escaping (Error?) -> Void) {
        NSLog("ModieshaExtensionProvider: Starting tunnel following SagerNet pattern")
        
        Task {
            do {
                try await startTunnelAsync(options: options)
                completionHandler(nil)
            } catch {
                NSLog("ModieshaExtensionProvider: Failed to start tunnel: \(error)")
                completionHandler(error)
            }
        }
    }
    
    override func stopTunnel(with reason: NEProviderStopReason, completionHandler: @escaping () -> Void) {
        NSLog("ModieshaExtensionProvider: Stopping tunnel, reason: \(reason)")
        
        Task {
            await stopTunnelAsync(with: reason)
            completionHandler()
        }
    }
    
    override func handleAppMessage(_ messageData: Data, completionHandler: ((Data?) -> Void)?) {
        NSLog("ModieshaExtensionProvider: Received app message")
        
        Task {
            let response = await handleAppMessageAsync(messageData)
            completionHandler?(response)
        }
    }
    
    // MARK: - Async Implementation (SagerNet Pattern)
    
    private func startTunnelAsync(options: [String: NSObject]?) async throws {
        // Step 1: Setup Libbox environment (following SagerNet pattern)
        try await setupLibboxEnvironment()
        
        // Step 2: Initialize platform interface
        setupPlatformInterface()
        
        // Step 3: Load configuration
        try await loadConfiguration(from: options)
        
        // Step 4: Start command server
        try await startCommandServer()
        
        // Step 5: Start sing-box service
        try await startService()
        
        NSLog("ModieshaExtensionProvider: Tunnel started successfully")
    }
    
    private func stopTunnelAsync(with reason: NEProviderStopReason) async {
        NSLog("ModieshaExtensionProvider: Stopping service...")
        
        // Stop service
        await stopService()
        
        // Stop command server
        await stopCommandServer()
        
        // Cleanup
        cleanup()
        
        NSLog("ModieshaExtensionProvider: Tunnel stopped")
    }
    
    private func handleAppMessageAsync(_ messageData: Data) async -> Data? {
        // Handle standard sing-box messages
        do {
            if let message = try JSONSerialization.jsonObject(with: messageData) as? [String: Any],
               let type = message["type"] as? String {
                
                switch type {
                case "status":
                    return try await getServiceStatus()
                case "reload":
                    try await reloadService()
                    return createSuccessResponse()
                default:
                    NSLog("ModieshaExtensionProvider: Unknown message type: \(type)")
                    return nil
                }
            }
        } catch {
            NSLog("ModieshaExtensionProvider: Failed to handle message: \(error)")
        }
        
        return nil
    }
    
    // MARK: - Libbox Setup (SagerNet Pattern)
    
    private func setupLibboxEnvironment() async throws {
        NSLog("ModieshaExtensionProvider: Setting up Libbox environment")
        
        // This follows the SagerNet ExtensionProvider.startTunnel pattern
        // When Libbox framework is properly linked, this will call:
        // LibboxClearServiceError()
        // LibboxSetup(options, &error)
        // LibboxRedirectStderr(logPath, &error)
        
        // For now, simulate the setup
        NSLog("ModieshaExtensionProvider: Libbox environment setup complete (simulated)")
    }
    
    private func setupPlatformInterface() {
        NSLog("ModieshaExtensionProvider: Setting up platform interface")
        
        // Initialize platform interface that handles TUN device setup
        platformInterface = ModieshaPlatformInterface(tunnelProvider: self)
        
        NSLog("ModieshaExtensionProvider: Platform interface ready")
    }
    
    private func loadConfiguration(from options: [String: NSObject]?) async throws {
        NSLog("ModieshaExtensionProvider: Loading configuration")
        
        // Get configuration from options or load from profile
        if let configData = options?["config"] as? Data,
           let configString = String(data: configData, encoding: .utf8) {
            configurationContent = configString
            NSLog("ModieshaExtensionProvider: Configuration loaded from options")
        } else {
            // Load from stored profile (SagerNet pattern)
            configurationContent = try await loadStoredConfiguration()
            NSLog("ModieshaExtensionProvider: Configuration loaded from profile")
        }
        
        guard configurationContent != nil else {
            throw NSError(domain: "ModieshaExtensionProvider", code: 1, 
                         userInfo: [NSLocalizedDescriptionKey: "No configuration available"])
        }
    }
    
    private func startCommandServer() async throws {
        NSLog("ModieshaExtensionProvider: Starting command server")
        
        // When Libbox is linked, this will be:
        // commandServer = await LibboxNewCommandServer(platformInterface, maxLogLines)
        // try commandServer.start()
        
        // For now, simulate
        NSLog("ModieshaExtensionProvider: Command server started (simulated)")
    }
    
    private func startService() async throws {
        NSLog("ModieshaExtensionProvider: Starting sing-box service")
        
        guard let configContent = configurationContent else {
            throw NSError(domain: "ModieshaExtensionProvider", code: 2,
                         userInfo: [NSLocalizedDescriptionKey: "No configuration content"])
        }
        
        // When Libbox is linked, this will be:
        // var error: NSError?
        // let service = LibboxNewService(configContent, platformInterface, &error)
        // try service.start()
        
        // For now, simulate service startup
        isServiceRunning = true
        NSLog("ModieshaExtensionProvider: sing-box service started (simulated)")
    }
    
    private func stopService() async {
        NSLog("ModieshaExtensionProvider: Stopping sing-box service")
        
        if isServiceRunning {
            // When Libbox is linked: try boxService.close()
            isServiceRunning = false
            NSLog("ModieshaExtensionProvider: sing-box service stopped")
        }
    }
    
    private func stopCommandServer() async {
        NSLog("ModieshaExtensionProvider: Stopping command server")
        
        // When Libbox is linked: try commandServer.close()
        commandServer = nil
        NSLog("ModieshaExtensionProvider: Command server stopped")
    }
    
    private func cleanup() {
        NSLog("ModieshaExtensionProvider: Cleaning up resources")
        
        boxService = nil
        platformInterface = nil
        configurationContent = nil
        
        NSLog("ModieshaExtensionProvider: Cleanup complete")
    }
    
    // MARK: - Helper Methods
    
    private func loadStoredConfiguration() async throws -> String {
        // This would load from ProfileManager in real implementation
        // For now, return a basic configuration
        return """
        {
            "log": {
                "level": "info"
            },
            "inbounds": [
                {
                    "type": "tun",
                    "tag": "tun-in",
                    "interface_name": "utun",
                    "inet4_address": "**********/30",
                    "auto_route": true,
                    "strict_route": false,
                    "stack": "system"
                }
            ],
            "outbounds": [
                {
                    "type": "direct",
                    "tag": "direct"
                }
            ]
        }
        """
    }
    
    private func getServiceStatus() async throws -> Data {
        let status = [
            "running": isServiceRunning,
            "uptime": isServiceRunning ? 100 : 0,
            "memory": 0
        ]
        return try JSONSerialization.data(withJSONObject: status)
    }
    
    private func createSuccessResponse() -> Data {
        let response = ["success": true]
        return try! JSONSerialization.data(withJSONObject: response)
    }
    
    private func reloadService() async throws {
        NSLog("ModieshaExtensionProvider: Reloading service")
        
        if isServiceRunning {
            await stopService()
            try await startService()
        }
        
        NSLog("ModieshaExtensionProvider: Service reloaded")
    }
}

// MARK: - Platform Interface

class ModieshaPlatformInterface {
    private weak var tunnelProvider: NEPacketTunnelProvider?
    
    init(tunnelProvider: NEPacketTunnelProvider) {
        self.tunnelProvider = tunnelProvider
    }
    
    // This will implement LibboxPlatformInterfaceProtocol when Libbox is linked
    // For now, provide basic functionality
    
    func writeLog(_ message: String) {
        NSLog("ModieshaPlatformInterface: \(message)")
    }
    
    func openTun() throws -> Int32 {
        // This will handle TUN device setup when Libbox is linked
        // For now, return a dummy file descriptor
        return -1
    }
}
