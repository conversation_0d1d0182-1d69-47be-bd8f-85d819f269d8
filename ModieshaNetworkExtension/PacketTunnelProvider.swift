import Foundation
import NetworkExtension

// Import Libbox framework when available
#if !targetEnvironment(simulator)
// Note: Uncomment the following line when Libbox.xcframework is properly linked to ModieshaNetworkExtension target
// import Libbox
#endif

/// PacketTunnelProvider implementation following SagerNet sing-box-for-apple pattern
/// This implements the proper sing-box integration directly
class PacketTunnelProvider: NEPacketTunnelProvider {

    // MARK: - Properties (SagerNet Pattern)
    #if !targetEnvironment(simulator)
    // When Libbox is properly linked, these will be the real types:
    // private var boxService: LibboxBoxService?
    // private var commandServer: LibboxCommandServer?
    // private var platformInterface: ExtensionPlatformInterface?
    #endif

    // For now, use Any to avoid compilation errors
    private var boxService: Any?
    private var commandServer: Any?
    private var platformInterface: Any?
    private var isServiceRunning = false
    private var configurationContent: String?

    // MARK: - NEPacketTunnelProvider Overrides

    override func startTunnel(options: [String: NSObject]?, completionHandler: @escaping (Error?) -> Void) {
        NSLog("PacketTunnelProvider: Starting tunnel with SagerNet sing-box pattern")

        Task {
            do {
                try await startTunnelAsync(options: options)
                completionHandler(nil)
            } catch {
                NSLog("PacketTunnelProvider: Failed to start tunnel: \(error)")
                completionHandler(error)
            }
        }
    }

    override func stopTunnel(with reason: NEProviderStopReason, completionHandler: @escaping () -> Void) {
        NSLog("PacketTunnelProvider: Stopping tunnel, reason: \(reason)")

        Task {
            await stopTunnelAsync(with: reason)
            completionHandler()
        }
    }

    override func handleAppMessage(_ messageData: Data, completionHandler: ((Data?) -> Void)?) {
        NSLog("PacketTunnelProvider: Received app message")

        Task {
            let response = await handleAppMessageAsync(messageData)
            completionHandler?(response)
        }
    }

    // MARK: - Async Implementation (SagerNet Pattern)

    private func startTunnelAsync(options: [String: NSObject]?) async throws {
        NSLog("PacketTunnelProvider: Starting tunnel async (SagerNet pattern)")

        // Run integration tests first
        runIntegrationTests()

        // Step 1: Setup Libbox environment
        try await setupLibboxEnvironment()

        // Step 2: Initialize platform interface
        setupPlatformInterface()

        // Step 3: Load configuration
        try await loadConfiguration(from: options)

        // Step 4: Setup tunnel network settings
        try await setupTunnelNetworkSettings()

        // Step 5: Start command server
        try await startCommandServer()

        // Step 6: Start sing-box service
        try await startService()

        NSLog("PacketTunnelProvider: Tunnel started successfully")
    }

    private func stopTunnelAsync(with reason: NEProviderStopReason) async {
        NSLog("PacketTunnelProvider: Stopping service...")

        // Stop service
        await stopService()

        // Stop command server
        await stopCommandServer()

        // Cleanup
        cleanup()

        NSLog("PacketTunnelProvider: Tunnel stopped")
    }

    private func handleAppMessageAsync(_ messageData: Data) async -> Data? {
        // Handle standard sing-box messages
        do {
            if let message = try JSONSerialization.jsonObject(with: messageData) as? [String: Any],
               let type = message["type"] as? String {

                switch type {
                case "status":
                    return try await getServiceStatus()
                case "reload":
                    try await reloadService()
                    return createSuccessResponse()
                default:
                    NSLog("PacketTunnelProvider: Unknown message type: \(type)")
                    return nil
                }
            }
        } catch {
            NSLog("PacketTunnelProvider: Failed to handle message: \(error)")
        }

        return nil
    }

    // MARK: - Additional NEPacketTunnelProvider Methods

    override func sleep() async {
        await super.sleep()
        // Pause service if running
        if isServiceRunning {
            NSLog("PacketTunnelProvider: Pausing service for sleep")
            // When Libbox is linked: boxService.pause()
        }
    }

    override func wake() {
        super.wake()
        // Resume service if it was running
        if isServiceRunning {
            NSLog("PacketTunnelProvider: Resuming service from sleep")
            // When Libbox is linked: boxService.wake()
        }
    }

    // MARK: - Implementation Methods (SagerNet Pattern)

    private func setupLibboxEnvironment() async throws {
        NSLog("PacketTunnelProvider: Setting up Libbox environment")

        #if !targetEnvironment(simulator)
        // When Libbox framework is properly linked, uncomment this:
        /*
        LibboxClearServiceError()

        let options = LibboxSetupOptions()
        options.basePath = getSharedDirectory().relativePath
        options.workingPath = getWorkingDirectory().relativePath
        options.tempPath = getCacheDirectory().relativePath

        var error: NSError?
        LibboxSetup(options, &error)
        if let error = error {
            throw error
        }

        LibboxRedirectStderr(getCacheDirectory().appendingPathComponent("stderr.log").relativePath, &error)
        if let error = error {
            throw error
        }
        */
        #endif

        // For now, simulate the setup
        NSLog("PacketTunnelProvider: Libbox environment setup complete (simulated)")
    }

    private func setupPlatformInterface() {
        NSLog("PacketTunnelProvider: Setting up platform interface")

        // Initialize platform interface that handles TUN device setup
        // When Libbox is linked: platformInterface = ExtensionPlatformInterface(self)
        platformInterface = self // Temporary placeholder

        NSLog("PacketTunnelProvider: Platform interface ready")
    }

    private func loadConfiguration(from options: [String: NSObject]?) async throws {
        NSLog("PacketTunnelProvider: Loading configuration")

        // Get configuration from options or load from profile
        if let configData = options?["config"] as? Data,
           let configString = String(data: configData, encoding: .utf8) {
            configurationContent = configString
            NSLog("PacketTunnelProvider: Configuration loaded from options")
        } else {
            // Load from stored profile (SagerNet pattern)
            configurationContent = try await loadStoredConfiguration()
            NSLog("PacketTunnelProvider: Configuration loaded from profile")
        }

        guard configurationContent != nil else {
            throw NSError(domain: "PacketTunnelProvider", code: 1,
                         userInfo: [NSLocalizedDescriptionKey: "No configuration available"])
        }
    }

    private func setupTunnelNetworkSettings() async throws {
        NSLog("PacketTunnelProvider: Setting up tunnel network settings")

        // Create basic tunnel settings
        let settings = NEPacketTunnelNetworkSettings(tunnelRemoteAddress: "127.0.0.1")

        // Configure IPv4 settings for TUN interface
        let ipv4Settings = NEIPv4Settings(addresses: ["********"], subnetMasks: ["*************"])
        ipv4Settings.includedRoutes = [NEIPv4Route.default()]
        ipv4Settings.excludedRoutes = [
            // Exclude local networks to prevent routing loops
            NEIPv4Route(destinationAddress: "10.0.0.0", subnetMask: "*********"),
            NEIPv4Route(destinationAddress: "**********", subnetMask: "***********"),
            NEIPv4Route(destinationAddress: "***********", subnetMask: "***********"),
            NEIPv4Route(destinationAddress: "*********", subnetMask: "*********")
        ]
        settings.ipv4Settings = ipv4Settings

        // Configure DNS settings
        let dnsSettings = NEDNSSettings(servers: ["*******", "*******"])
        dnsSettings.matchDomains = [""]
        dnsSettings.matchDomainsNoSearch = true
        settings.dnsSettings = dnsSettings

        // Set MTU
        settings.mtu = 1500

        // Apply settings
        try await setTunnelNetworkSettings(settings)
        NSLog("PacketTunnelProvider: Tunnel network settings applied")
    }

    private func startCommandServer() async throws {
        NSLog("PacketTunnelProvider: Starting command server")

        // When Libbox is linked, this will be:
        // commandServer = await LibboxNewCommandServer(platformInterface, maxLogLines)
        // try commandServer.start()

        // For now, simulate
        NSLog("PacketTunnelProvider: Command server started (simulated)")
    }

    private func startService() async throws {
        NSLog("PacketTunnelProvider: Starting sing-box service")

        guard let configContent = configurationContent else {
            throw NSError(domain: "PacketTunnelProvider", code: 2,
                         userInfo: [NSLocalizedDescriptionKey: "No configuration content"])
        }

        #if !targetEnvironment(simulator)
        // When Libbox is linked, uncomment this:
        /*
        var error: NSError?
        let service = LibboxNewService(configContent, platformInterface, &error)
        if let error = error {
            throw error
        }
        guard let service = service else {
            throw NSError(domain: "PacketTunnelProvider", code: 3, userInfo: [NSLocalizedDescriptionKey: "Failed to create Libbox service"])
        }

        try service.start()

        // Set service in command server
        if let commandServer = commandServer as? LibboxCommandServer {
            commandServer.setService(service)
        }

        boxService = service
        */
        #endif

        // For now, simulate service startup
        isServiceRunning = true
        NSLog("PacketTunnelProvider: sing-box service started (simulated) with config: \(configContent.prefix(100))...")
    }

    private func stopService() async {
        NSLog("PacketTunnelProvider: Stopping sing-box service")

        if isServiceRunning {
            // When Libbox is linked: try boxService.close()
            isServiceRunning = false
            NSLog("PacketTunnelProvider: sing-box service stopped")
        }
    }

    private func stopCommandServer() async {
        NSLog("PacketTunnelProvider: Stopping command server")

        // When Libbox is linked: try commandServer.close()
        commandServer = nil
        NSLog("PacketTunnelProvider: Command server stopped")
    }

    private func cleanup() {
        NSLog("PacketTunnelProvider: Cleaning up resources")

        boxService = nil
        platformInterface = nil
        configurationContent = nil

        NSLog("PacketTunnelProvider: Cleanup complete")
    }

    // MARK: - Helper Methods

    private func loadStoredConfiguration() async throws -> String {
        // This would load from ProfileManager in real implementation
        // For now, return a basic sing-box configuration
        return """
        {
            "log": {
                "level": "info",
                "timestamp": true
            },
            "inbounds": [
                {
                    "type": "tun",
                    "tag": "tun-in",
                    "interface_name": "utun",
                    "inet4_address": "**********/30",
                    "inet6_address": "fdfe:dcba:9876::1/126",
                    "auto_route": true,
                    "strict_route": false,
                    "stack": "system",
                    "sniff": true,
                    "sniff_override_destination": true
                }
            ],
            "outbounds": [
                {
                    "type": "direct",
                    "tag": "direct"
                },
                {
                    "type": "dns",
                    "tag": "dns-out"
                }
            ],
            "route": {
                "rules": [
                    {
                        "protocol": "dns",
                        "outbound": "dns-out"
                    }
                ],
                "auto_detect_interface": true
            },
            "dns": {
                "servers": [
                    {
                        "tag": "google",
                        "address": "*******"
                    }
                ],
                "rules": [],
                "final": "google",
                "independent_cache": true
            }
        }
        """
    }

    private func getServiceStatus() async throws -> Data {
        let status: [String: Any] = [
            "running": isServiceRunning,
            "uptime": isServiceRunning ? 100 : 0,
            "memory": 0,
            "connections": 0
        ]
        return try JSONSerialization.data(withJSONObject: status)
    }

    private func createSuccessResponse() -> Data {
        let response = ["success": true]
        return try! JSONSerialization.data(withJSONObject: response)
    }

    private func reloadService() async throws {
        NSLog("PacketTunnelProvider: Reloading service")

        if isServiceRunning {
            await stopService()
            try await startService()
        }

        NSLog("PacketTunnelProvider: Service reloaded")
    }

    // MARK: - Testing and Helper Methods

//    private func runIntegrationTests() {
//        NSLog("PacketTunnelProvider: ✅ SagerNet sing-box integration ready")
//        NSLog("PacketTunnelProvider: ✅ Tunnel lifecycle implemented")
//        NSLog("PacketTunnelProvider: ✅ Platform interface ready")
//        NSLog("PacketTunnelProvider: ✅ Configuration management ready")
//        NSLog("PacketTunnelProvider: ✅ Message handling implemented")
//        NSLog("PacketTunnelProvider: 🔄 Waiting for Libbox framework linkage for full functionality")
//    }

    // MARK: - Directory Helper Methods

    private func getSharedDirectory() -> URL {
        // Use app group container for shared data
        let groupIdentifier = "group.cn.seungyu.modiesha"
        return FileManager.default.containerURL(forSecurityApplicationGroupIdentifier: groupIdentifier)
            ?? FileManager.default.temporaryDirectory
    }

    private func getWorkingDirectory() -> URL {
        return getSharedDirectory().appendingPathComponent("working", isDirectory: true)
    }

    private func getCacheDirectory() -> URL {
        return getSharedDirectory().appendingPathComponent("cache", isDirectory: true)
    }
}

