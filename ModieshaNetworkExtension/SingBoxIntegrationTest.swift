import Foundation
import NetworkExtension

/// Test class to verify the sing-box integration is working correctly
class SingBoxIntegrationTest {
    
    static func testConfiguration() -> <PERSON><PERSON> {
        NSLog("SingBoxIntegrationTest: Testing configuration parsing")
        
        let testConfig = """
        {
            "log": {
                "level": "info"
            },
            "inbounds": [
                {
                    "type": "tun",
                    "tag": "tun-in",
                    "interface_name": "utun",
                    "inet4_address": "**********/30",
                    "auto_route": true,
                    "stack": "system"
                }
            ],
            "outbounds": [
                {
                    "type": "direct",
                    "tag": "direct"
                }
            ]
        }
        """
        
        guard let configData = testConfig.data(using: .utf8) else {
            NSLog("SingBoxIntegrationTest: Failed to create config data")
            return false
        }
        
        do {
            let jsonObject = try JSONSerialization.jsonObject(with: configData)
            NSLog("SingBoxIntegrationTest: Configuration parsing successful")
            return true
        } catch {
            NSLog("SingBoxIntegrationTest: Configuration parsing failed: \(error)")
            return false
        }
    }
    
    static func testTunnelSettings() -> Bool {
        NSLog("SingBoxIntegrationTest: Testing tunnel network settings")
        
        let settings = NEPacketTunnelNetworkSettings(tunnelRemoteAddress: "127.0.0.1")
        
        // Configure IPv4 settings
        let ipv4Settings = NEIPv4Settings(addresses: ["********"], subnetMasks: ["*************"])
        ipv4Settings.includedRoutes = [NEIPv4Route.default()]
        settings.ipv4Settings = ipv4Settings
        
        // Configure DNS settings
        let dnsSettings = NEDNSSettings(servers: ["*******", "*******"])
        dnsSettings.matchDomains = [""]
        settings.dnsSettings = dnsSettings
        
        // Set MTU
        settings.mtu = 1500
        
        NSLog("SingBoxIntegrationTest: Tunnel settings created successfully")
        return true
    }
    
    static func testMessageHandling() -> Bool {
        NSLog("SingBoxIntegrationTest: Testing message handling")
        
        let statusMessage = ["type": "status"]
        let reloadMessage = ["type": "reload"]
        
        do {
            let statusData = try JSONSerialization.data(withJSONObject: statusMessage)
            let reloadData = try JSONSerialization.data(withJSONObject: reloadMessage)
            
            NSLog("SingBoxIntegrationTest: Message serialization successful")
            return true
        } catch {
            NSLog("SingBoxIntegrationTest: Message handling failed: \(error)")
            return false
        }
    }
    
    static func runAllTests() -> Bool {
        NSLog("SingBoxIntegrationTest: Running all integration tests")
        
        let configTest = testConfiguration()
        let tunnelTest = testTunnelSettings()
        let messageTest = testMessageHandling()
        
        let allPassed = configTest && tunnelTest && messageTest
        
        NSLog("SingBoxIntegrationTest: All tests \(allPassed ? "PASSED" : "FAILED")")
        return allPassed
    }
}

/// Extension to PacketTunnelProvider for testing
extension PacketTunnelProvider {
    
    func runIntegrationTests() {
        NSLog("PacketTunnelProvider: Running integration tests")
        
        let testsPassed = SingBoxIntegrationTest.runAllTests()
        
        if testsPassed {
            NSLog("PacketTunnelProvider: ✅ All integration tests passed")
        } else {
            NSLog("PacketTunnelProvider: ❌ Some integration tests failed")
        }
    }
}
